-- 临时邮箱服务数据库表结构
-- 数据库: cursor

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '用户邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_admin BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    daily_mailbox_limit INT DEFAULT 10 COMMENT '每日邮箱创建限制',
    used_mailboxes_today INT DEFAULT 0 COMMENT '今日已使用邮箱数',
    last_reset_date DATE COMMENT '最后重置日期',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 授权码表
CREATE TABLE IF NOT EXISTS auth_codes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '授权码',
    code_type ENUM('EMAIL_VERIFICATION', 'MAILBOX_50', 'MAILBOX_100', 'MAILBOX_500', 'MAILBOX_1000') NOT NULL DEFAULT 'EMAIL_VERIFICATION' COMMENT '授权码类型',
    mailbox_quota INT DEFAULT 0 COMMENT '邮箱配额（0表示邮箱验证码）',
    email VARCHAR(255) NULL COMMENT '邮箱地址（用于注册验证）',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已使用',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    used_by_user_id INT NULL COMMENT '使用者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_by_admin_id INT NULL COMMENT '创建者管理员ID',
    FOREIGN KEY (used_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by_admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_code (code),
    INDEX idx_code_type (code_type),
    INDEX idx_email (email),
    INDEX idx_is_used (is_used),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='授权码表';

-- 3. 邮箱表
CREATE TABLE IF NOT EXISTS mailboxes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT '用户ID（临时邮箱可为空）',
    email_address VARCHAR(255) UNIQUE NOT NULL COMMENT '邮箱地址',
    display_name VARCHAR(100) COMMENT '显示名称',
    client_ip VARCHAR(45) COMMENT '客户端IP（用于临时邮箱限制）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_checked TIMESTAMP NULL COMMENT '最后检查时间',
    total_emails INT DEFAULT 0 COMMENT '总邮件数',
    unread_emails INT DEFAULT 0 COMMENT '未读邮件数',
    auth_code_id INT NULL COMMENT '使用的授权码ID',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (auth_code_id) REFERENCES auth_codes(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_email_address (email_address),
    INDEX idx_client_ip (client_ip),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱表';

-- 4. 邮件表
CREATE TABLE IF NOT EXISTS emails (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mailbox_id INT NOT NULL COMMENT '邮箱ID',
    pop3_message_id INT COMMENT 'POP3服务器消息ID',
    message_id VARCHAR(255) COMMENT '邮件Message-ID',
    subject TEXT COMMENT '邮件主题',
    from_addr VARCHAR(255) NOT NULL COMMENT '发件人地址',
    to_addr VARCHAR(255) NOT NULL COMMENT '收件人地址',
    cc_addr TEXT COMMENT '抄送地址',
    bcc_addr TEXT COMMENT '密送地址',
    reply_to VARCHAR(255) COMMENT '回复地址',
    content_text LONGTEXT COMMENT '纯文本内容',
    content_html LONGTEXT COMMENT 'HTML内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    is_forwarded BOOLEAN DEFAULT FALSE COMMENT '是否为转发邮件',
    original_from VARCHAR(255) COMMENT '原始发件人（转发邮件）',
    original_to VARCHAR(255) COMMENT '原始收件人（转发邮件）',
    email_date TIMESTAMP NULL COMMENT '邮件发送时间',
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间',
    size_bytes INT DEFAULT 0 COMMENT '邮件大小（字节）',
    has_attachments BOOLEAN DEFAULT FALSE COMMENT '是否有附件',
    priority ENUM('low', 'normal', 'high') DEFAULT 'normal' COMMENT '优先级',
    spam_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '垃圾邮件评分',
    is_spam BOOLEAN DEFAULT FALSE COMMENT '是否为垃圾邮件',
    raw_headers TEXT COMMENT '原始邮件头',
    FOREIGN KEY (mailbox_id) REFERENCES mailboxes(id) ON DELETE CASCADE,
    INDEX idx_mailbox_id (mailbox_id),
    INDEX idx_from_addr (from_addr),
    INDEX idx_to_addr (to_addr),
    INDEX idx_is_read (is_read),
    INDEX idx_email_date (email_date),
    INDEX idx_received_at (received_at),
    INDEX idx_message_id (message_id),
    INDEX idx_pop3_message_id (pop3_message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件表';

-- 5. 邮件附件表（预留，暂不实现）
CREATE TABLE IF NOT EXISTS email_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email_id INT NOT NULL COMMENT '邮件ID',
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    content_type VARCHAR(100) COMMENT '内容类型',
    size_bytes INT DEFAULT 0 COMMENT '文件大小',
    file_path VARCHAR(500) COMMENT '文件存储路径',
    is_inline BOOLEAN DEFAULT FALSE COMMENT '是否为内联附件',
    content_id VARCHAR(255) COMMENT '内容ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE,
    INDEX idx_email_id (email_id),
    INDEX idx_filename (filename)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件附件表';

-- 6. 转发规则表
CREATE TABLE IF NOT EXISTS forwarding_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mailbox_id INT NOT NULL COMMENT '邮箱ID',
    target_email VARCHAR(255) NOT NULL COMMENT '转发目标邮箱',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    forward_all BOOLEAN DEFAULT TRUE COMMENT '是否转发所有邮件',
    filter_keywords TEXT COMMENT '过滤关键词（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (mailbox_id) REFERENCES mailboxes(id) ON DELETE CASCADE,
    INDEX idx_mailbox_id (mailbox_id),
    INDEX idx_target_email (target_email),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转发规则表';

-- 7. 域名表
CREATE TABLE IF NOT EXISTS domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain_name VARCHAR(255) UNIQUE NOT NULL COMMENT '域名',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认域名',
    max_mailboxes INT DEFAULT 1000 COMMENT '最大邮箱数量',
    current_mailboxes INT DEFAULT 0 COMMENT '当前邮箱数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_domain_name (domain_name),
    INDEX idx_is_active (is_active),
    INDEX idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名表';

-- 8. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否为公开配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 9. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT '用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc TEXT COMMENT '操作描述',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id INT COMMENT '目标ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at),
    INDEX idx_target (target_type, target_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
