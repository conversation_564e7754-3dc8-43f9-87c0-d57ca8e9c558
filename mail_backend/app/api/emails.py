"""
邮件相关API端点
"""

from fastapi import APIRouter, Query, HTTPException
from typing import List, Dict, Any, Optional
import logging

from app.services.pop3_service import pop3_service
from app.database.connection import get_db_connection

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[Dict[str, Any]])
async def get_emails(
    hours: Optional[int] = Query(24, description="时间过滤（小时）", ge=1, le=168),
    limit: int = Query(50, description="邮件数量限制", ge=1, le=100)
):
    """
    获取邮件列表
    
    - **hours**: 时间过滤，获取最近N小时的邮件（1-168小时）
    - **limit**: 邮件数量限制（1-100封）
    """
    try:
        emails = await pop3_service.get_emails(hours=hours, limit=limit)
        return emails
    except Exception as e:
        logger.error(f"获取邮件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取邮件失败: {str(e)}")


@router.get("/recent", response_model=Dict[str, Any])
async def get_recent_emails(
    mailbox_id: Optional[int] = Query(None, description="邮箱ID"),
    hours: int = Query(1, description="时间过滤（小时）", ge=1, le=24),
    limit: int = Query(20, description="邮件数量限制", ge=1, le=50)
):
    """
    快速获取最近邮件

    - **mailbox_id**: 邮箱ID（可选，如果不提供则获取所有邮件）
    - **hours**: 时间过滤，获取最近N小时的邮件（1-24小时）
    - **limit**: 邮件数量限制（1-50封）
    """
    from app.database.connection import get_db_connection
    from datetime import datetime, timedelta

    try:
        async with get_db_connection() as conn:
            async with conn.cursor() as cursor:
                # 如果提供了邮箱ID，则只获取该邮箱的邮件
                if mailbox_id:
                    # 检查邮箱是否存在
                    await cursor.execute("SELECT email_address FROM mailboxes WHERE id = %s", (mailbox_id,))
                    mailbox = await cursor.fetchone()
                    if not mailbox:
                        raise HTTPException(status_code=404, detail="邮箱不存在")

                    # 获取该邮箱的邮件
                    time_filter = datetime.now() - timedelta(hours=hours)
                    await cursor.execute("""
                        SELECT id, subject, from_addr as sender, to_addr as recipient,
                               content_text as content, received_at, is_read, mailbox_id
                        FROM emails
                        WHERE mailbox_id = %s AND received_at >= %s
                        ORDER BY received_at DESC
                        LIMIT %s
                    """, (mailbox_id, time_filter, limit))

                    emails = await cursor.fetchall()
                    email_list = []
                    for email in emails:
                        email_list.append({
                            "id": email[0],
                            "subject": email[1] or "无主题",
                            "sender": email[2],
                            "recipient": email[3],
                            "content": email[4] or "",
                            "received_at": email[5].isoformat() if email[5] else None,
                            "is_read": bool(email[6]),
                            "mailbox": {"id": email[7], "email": mailbox[0]}
                        })

                    return {
                        "success": True,
                        "data": {
                            "items": email_list,
                            "total": len(email_list),
                            "page": 1,
                            "limit": limit
                        }
                    }
                else:
                    # 如果没有提供邮箱ID，返回空结果
                    return {
                        "success": True,
                        "data": {
                            "items": [],
                            "total": 0,
                            "page": 1,
                            "limit": limit
                        },
                        "message": "请先获取邮箱"
                    }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取最近邮件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近邮件失败: {str(e)}")


@router.delete("/{email_id}", response_model=Dict[str, Any])
async def delete_email(email_id: int):
    """
    删除邮件
    """
    from app.database.connection import get_db_connection

    try:
        async with get_db_connection() as conn:
            async with conn.cursor() as cursor:
                # 检查邮件是否存在
                await cursor.execute("SELECT id FROM emails WHERE id = %s", (email_id,))
                email = await cursor.fetchone()
                if not email:
                    raise HTTPException(status_code=404, detail="邮件不存在")

                # 删除邮件
                await cursor.execute("DELETE FROM emails WHERE id = %s", (email_id,))
                await conn.commit()

                return {
                    "success": True,
                    "message": "邮件删除成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除邮件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除邮件失败: {str(e)}")


@router.post("/sync", response_model=Dict[str, Any])
async def sync_emails():
    """
    手动同步邮件
    """
    try:
        from app.tasks.email_sync_task import email_sync_task
        await email_sync_task.sync_once()

        return {
            "success": True,
            "message": "邮件同步完成"
        }

    except Exception as e:
        logger.error(f"手动同步邮件失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步邮件失败: {str(e)}")


@router.get("/today", response_model=List[Dict[str, Any]])
async def get_today_emails(
    limit: int = Query(50, description="邮件数量限制", ge=1, le=100)
):
    """
    获取今天的邮件（24小时内）
    
    - **limit**: 邮件数量限制（1-100封）
    """
    try:
        emails = await pop3_service.get_today_emails(limit=limit)
        return emails
    except Exception as e:
        logger.error(f"获取今天邮件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取今天邮件失败: {str(e)}")


@router.get("/{email_id}", response_model=Dict[str, Any])
async def get_email_detail(email_id: int):
    """
    获取邮件详情
    
    - **email_id**: 邮件ID
    """
    try:
        # 获取所有邮件，然后找到指定ID的邮件
        emails = await pop3_service.get_emails(hours=168, limit=100)  # 获取一周内的邮件
        
        for email in emails:
            if email.get('id') == email_id:
                return email
        
        raise HTTPException(status_code=404, detail="邮件不存在")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮件详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取邮件详情失败: {str(e)}")


@router.put("/{email_id}/read")
async def mark_email_read(email_id: int):
    """标记邮件为已读"""
    try:
        async with get_db_connection() as connection:
            async with connection.cursor() as cursor:
                # 检查邮件是否存在
                await cursor.execute("SELECT id FROM emails WHERE id = %s", (email_id,))
                email = await cursor.fetchone()

                if not email:
                    raise HTTPException(status_code=404, detail="邮件不存在")

                # 标记为已读
                await cursor.execute("UPDATE emails SET is_read = 1 WHERE id = %s", (email_id,))
                await connection.commit()

                return {
                    "success": True,
                    "message": "邮件已标记为已读"
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标记邮件已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"标记邮件已读失败: {str(e)}")


@router.delete("/{email_id}")
async def delete_email(email_id: int):
    """删除邮件"""
    try:
        async with get_db_connection() as connection:
            async with connection.cursor() as cursor:
                # 检查邮件是否存在
                await cursor.execute("SELECT id FROM emails WHERE id = %s", (email_id,))
                email = await cursor.fetchone()

                if not email:
                    raise HTTPException(status_code=404, detail="邮件不存在")

                # 删除邮件
                await cursor.execute("DELETE FROM emails WHERE id = %s", (email_id,))
                await connection.commit()

                return {
                    "success": True,
                    "message": "邮件删除成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除邮件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除邮件失败: {str(e)}")


@router.get("/stats/summary")
async def get_email_stats():
    """
    获取邮件统计信息
    """
    try:
        # 获取不同时间段的邮件数量
        recent_1h = await pop3_service.get_recent_emails(hours=1, limit=100)
        recent_6h = await pop3_service.get_recent_emails(hours=6, limit=100)
        today = await pop3_service.get_today_emails(limit=100)

        return {
            "recent_1_hour": len(recent_1h),
            "recent_6_hours": len(recent_6h),
            "today": len(today),
            "last_check": "刚刚"
        }
    except Exception as e:
        logger.error(f"获取邮件统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取邮件统计失败: {str(e)}")
