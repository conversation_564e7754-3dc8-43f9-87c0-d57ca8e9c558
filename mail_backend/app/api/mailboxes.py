"""
邮箱相关API端点
"""

from fastapi import APIRouter, HTTPException, Request
from typing import Dict, Any

router = APIRouter()


@router.post("/temp", response_model=Dict[str, Any])
async def get_temp_mailbox(request: Request):
    """
    获取临时邮箱（未注册用户）
    每个IP每天限制1个临时邮箱
    """
    from app.database.connection import get_db_connection
    import secrets
    import string
    from datetime import datetime, timedelta

    # 获取真实客户端IP（处理代理和负载均衡）
    def get_real_ip(request):
        # 优先级：X-Real-IP > X-Forwarded-For > client.host
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip

        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            # X-Forwarded-For可能包含多个IP，取第一个（真实客户端IP）
            return forwarded_for.split(',')[0].strip()

        # 兜底使用直连IP
        return request.client.host

    client_ip = get_real_ip(request)

    try:
        async with get_db_connection() as conn:
            async with conn.cursor() as cursor:
                # 检查今天该IP是否已经获取过临时邮箱
                today = datetime.now().date()
                await cursor.execute("""
                    SELECT COUNT(*) FROM mailboxes
                    WHERE client_ip = %s AND DATE(created_at) = %s AND user_id IS NULL
                """, (client_ip, today))

                count = (await cursor.fetchone())[0]
                if count >= 1:
                    # 如果已经有邮箱了，返回现有的邮箱
                    await cursor.execute("""
                        SELECT id, email_address, expires_at, created_at
                        FROM mailboxes
                        WHERE client_ip = %s AND DATE(created_at) = %s AND user_id IS NULL
                        ORDER BY created_at DESC LIMIT 1
                    """, (client_ip, today))

                    existing_mailbox = await cursor.fetchone()
                    if existing_mailbox:
                        return {
                            "success": True,
                            "message": "返回今日已创建的临时邮箱",
                            "data": {
                                "id": existing_mailbox[0],
                                "email": existing_mailbox[1],
                                "expires_at": existing_mailbox[2].isoformat() if existing_mailbox[2] else None,
                                "created_at": existing_mailbox[3].isoformat() if existing_mailbox[3] else None
                            }
                        }
                    else:
                        raise HTTPException(
                            status_code=400,
                            detail="每个IP每天只能获取1个临时邮箱"
                        )

                # 生成随机邮箱地址
                chars = string.ascii_lowercase + string.digits
                random_part = ''.join(secrets.choice(chars) for _ in range(8))
                email_address = f"{random_part}@32hgh48dfd.shop"

                # 设置过期时间（24小时后）
                expires_at = datetime.now() + timedelta(hours=24)

                # 创建临时邮箱
                await cursor.execute("""
                    INSERT INTO mailboxes (email_address, client_ip, expires_at, created_at)
                    VALUES (%s, %s, %s, %s)
                """, (email_address, client_ip, expires_at, datetime.now()))

                mailbox_id = cursor.lastrowid
                await conn.commit()

                return {
                    "success": True,
                    "message": "临时邮箱创建成功",
                    "data": {
                        "id": mailbox_id,
                        "email": email_address,
                        "expires_at": expires_at.isoformat(),
                        "created_at": datetime.now().isoformat()
                    }
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"获取临时邮箱失败: {str(e)}")


# 其他路由暂时移除，专注于临时邮箱功能
