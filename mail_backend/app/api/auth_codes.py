"""
授权码相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Dict, Any, Optional
from enum import Enum

from app.database.session import get_db
from app.services.auth_code_service import AuthCodeService
from app.models.auth_code import AuthCode, AuthCodeType
from app.api.dependencies import get_current_user_id

router = APIRouter()


class CodeTypeEnum(str, Enum):
    """授权码类型枚举（API用）"""
    MAILBOX_50 = "MAILBOX_50"
    MAILBOX_100 = "MAILBOX_100"
    MAILBOX_500 = "MAILBOX_500"
    MAILBOX_1000 = "MAILBOX_1000"


class CreateAuthCodesRequest(BaseModel):
    code_type: CodeTypeEnum
    count: int
    expires_days: int = 365


class UseAuthCodeRequest(BaseModel):
    code: str


@router.post("/create", response_model=Dict[str, Any])
async def create_auth_codes(
    request: CreateAuthCodesRequest,
    db: AsyncSession = Depends(get_db),
    admin_user_id: int = Depends(get_current_user_id)  # 需要管理员权限
):
    """
    创建邮箱授权码（管理员功能）
    支持创建50、100、500、1000邮箱的授权码
    """
    if request.count <= 0 or request.count > 1000:
        raise HTTPException(status_code=400, detail="数量必须在1-1000之间")
    
    if request.expires_days <= 0 or request.expires_days > 3650:
        raise HTTPException(status_code=400, detail="有效期必须在1-3650天之间")
    
    # 转换枚举类型
    code_type = AuthCodeType(request.code_type.value)
    
    result = await AuthCodeService.create_mailbox_auth_codes(
        db=db,
        code_type=code_type,
        count=request.count,
        admin_user_id=admin_user_id,
        expires_days=request.expires_days
    )
    
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    
    return result


@router.post("/use", response_model=Dict[str, Any])
async def use_auth_code(
    request: UseAuthCodeRequest,
    user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    使用邮箱授权码
    使用后会增加用户的每日邮箱配额
    """
    if not request.code or len(request.code) < 8:
        raise HTTPException(status_code=400, detail="授权码格式不正确")
    
    result = await AuthCodeService.use_mailbox_auth_code(
        db=db,
        code=request.code.upper(),  # 转换为大写
        user_id=user_id
    )
    
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    
    return result


@router.get("/stats", response_model=Dict[str, Any])
async def get_auth_code_stats(
    db: AsyncSession = Depends(get_db),
    admin_user_id: int = Depends(get_current_user_id)  # 需要管理员权限
):
    """
    获取授权码统计信息（管理员功能）
    """
    result = await AuthCodeService.get_auth_code_stats(db=db)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["message"])
    
    return result


@router.get("/types", response_model=Dict[str, Any])
async def get_auth_code_types():
    """
    获取可用的授权码类型和配额
    """
    types = {}
    for code_type in AuthCodeType:
        if code_type != AuthCodeType.EMAIL_VERIFICATION:
            quota = AuthCode.get_quota_by_type(code_type)
            types[code_type.value] = {
                "name": f"{quota}邮箱授权码",
                "quota": quota,
                "description": f"使用后可获得{quota}个邮箱配额"
            }
    
    return {
        "success": True,
        "types": types
    }
