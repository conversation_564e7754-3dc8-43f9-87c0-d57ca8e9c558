"""
用户相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from typing import Dict, Any

from app.database.session import get_db
from app.services.auth_service import auth_service
from app.services.mailbox_service import mailbox_service

router = APIRouter()


async def get_current_user_id(
    db: AsyncSession = Depends(get_db)
) -> int:
    """获取当前用户ID（临时返回测试用户ID）"""
    # TODO: 实现真正的JWT认证
    return 1  # 临时返回测试用户ID


@router.get("/me", response_model=Dict[str, Any])
async def get_current_user(
    user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户信息
    """
    # 获取用户信息（临时使用固定用户）
    from sqlalchemy import select
    from app.models.user import User

    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 计算今天已使用的邮箱数量
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    used_today = await user.get_mailboxes_created_today(db, today_start)

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "is_active": user.is_active,
        "daily_mailbox_limit": user.daily_mailbox_limit,
        "used_mailboxes_today": used_today,
        "remaining_mailboxes_today": max(0, user.daily_mailbox_limit - used_today),
        "created_at": user.created_at.isoformat() if user.created_at else None,
        "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None
    }


@router.get("/stats", response_model=Dict[str, Any])
async def get_user_stats(
    user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户统计信息
    """
    # 获取邮箱统计
    mailbox_stats = await mailbox_service.get_mailbox_stats(db=db, user_id=user_id)

    # TODO: 添加邮件统计
    # 这里可以添加总邮件数、未读邮件数等统计

    return {
        "total_mailboxes": mailbox_stats["total_mailboxes"],
        "active_mailboxes": mailbox_stats["active_mailboxes"],
        "today_created_mailboxes": mailbox_stats["today_created"],
        "total_emails": 0,  # TODO: 实现邮件统计
        "unread_emails": 0,  # TODO: 实现邮件统计
        "today_emails": 0   # TODO: 实现邮件统计
    }


@router.post("/auth-code", response_model=Dict[str, Any])
async def generate_auth_code(
    user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    生成授权码（用于创建邮箱）
    """
    try:
        code = await auth_service.generate_auth_code(db=db, user_id=user_id)
        return {
            "success": True,
            "auth_code": code,
            "message": "授权码生成成功，24小时内有效"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
