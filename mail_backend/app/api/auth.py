"""
认证相关API端点
"""

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import Dict, Any

from app.services.auth_service import AuthService
from app.core.email_domains import get_domain_categories, get_allowed_domains, get_popular_domains

router = APIRouter()


class SendCodeRequest(BaseModel):
    email: str

class RegisterRequest(BaseModel):
    email: str
    password: str
    verification_code: str

class LoginRequest(BaseModel):
    email: str
    password: str


@router.post("/send-code", response_model=Dict[str, Any])
async def send_verification_code(
    request: SendCodeRequest
):
    """
    发送邮箱验证码
    """
    result = await AuthService.send_verification_code(
        email=request.email
    )

    # 直接返回统一格式，不抛出异常
    return result


@router.post("/register", response_model=Dict[str, Any])
async def register(
    request: RegisterRequest
):
    """
    用户注册（邮箱+密码+验证码）
    注册成功后自动分配3个临时邮箱，每天可申请10个
    """
    result = await AuthService.register_user(
        email=request.email,
        password=request.password,
        verification_code=request.verification_code
    )

    # 直接返回统一格式，不抛出异常
    return result


@router.post("/login", response_model=Dict[str, Any])
async def login(
    request: LoginRequest
):
    """
    用户登录（邮箱+密码）
    """
    result = await AuthService.login_user(
        email=request.email,
        password=request.password
    )

    # 直接返回统一格式，不抛出异常
    return result


@router.get("/allowed-domains", response_model=Dict[str, Any])
async def get_allowed_email_domains():
    """
    获取允许注册的邮箱域名列表
    """
    domains = get_allowed_domains()
    categories = get_domain_categories()
    popular = get_popular_domains(10)

    return {
        "success": True,
        "message": "获取允许的邮箱域名成功",
        "data": {
            "all_domains": sorted(list(domains)),
            "categories": categories,
            "popular": popular,
            "total_count": len(domains),
            "examples": {
                "domestic": "<EMAIL>, <EMAIL>",
                "international": "<EMAIL>, <EMAIL>",
                "enterprise": "<EMAIL>"
            }
        }
    }
