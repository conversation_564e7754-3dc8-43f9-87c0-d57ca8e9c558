"""
邮件同步定时任务
"""

import asyncio
import logging
from datetime import datetime

from app.services.email_sync_service import email_sync_service

logger = logging.getLogger(__name__)


class EmailSyncTask:
    """邮件同步定时任务"""
    
    def __init__(self, interval_seconds: int = 60):
        self.interval_seconds = interval_seconds  # 同步间隔（秒）
        self.is_running = False
        self.task = None
    
    async def start(self):
        """启动定时任务"""
        if self.is_running:
            logger.warning("邮件同步任务已在运行")
            return
        
        self.is_running = True
        self.task = asyncio.create_task(self._run_sync_loop())
        logger.info(f"邮件同步任务已启动，间隔 {self.interval_seconds} 秒")
    
    async def stop(self):
        """停止定时任务"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        logger.info("邮件同步任务已停止")
    
    async def _run_sync_loop(self):
        """运行同步循环"""
        while self.is_running:
            try:
                start_time = datetime.now()
                logger.info("开始邮件同步...")
                
                await email_sync_service.sync_all_active_mailboxes()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.info(f"邮件同步完成，耗时 {duration:.2f} 秒")
                
            except Exception as e:
                logger.error(f"邮件同步任务出错: {e}")
            
            # 等待下次同步
            await asyncio.sleep(self.interval_seconds)
    
    async def sync_once(self):
        """手动执行一次同步"""
        logger.info("手动执行邮件同步...")
        await email_sync_service.sync_all_active_mailboxes()


# 创建全局任务实例
email_sync_task = EmailSyncTask(interval_seconds=5)  # 每5秒同步一次
