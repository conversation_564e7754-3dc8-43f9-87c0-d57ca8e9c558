"""
数据库连接管理
"""

import aiomysql
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from app.core.config import settings


@asynccontextmanager
async def get_db_connection() -> AsyncGenerator[aiomysql.Connection, None]:
    """获取数据库连接"""
    connection = None
    try:
        connection = await aiomysql.connect(
            host=settings.DATABASE_HOST,
            port=settings.DATABASE_PORT,
            user=settings.DATABASE_USER,
            password=settings.DATABASE_PASSWORD,
            db=settings.DATABASE_NAME,
            charset='utf8mb4',
            autocommit=False
        )
        yield connection
    except Exception as e:
        if connection:
            await connection.rollback()
        raise e
    finally:
        if connection:
            await connection.ensure_closed()


async def get_db_pool():
    """获取数据库连接池"""
    return await aiomysql.create_pool(
        host=settings.DATABASE_HOST,
        port=settings.DATABASE_PORT,
        user=settings.DATABASE_USER,
        password=settings.DATABASE_PASSWORD,
        db=settings.DATABASE_NAME,
        charset='utf8mb4',
        autocommit=False,
        minsize=1,
        maxsize=10
    )
