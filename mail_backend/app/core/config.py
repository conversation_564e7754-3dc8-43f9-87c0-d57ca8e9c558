"""
应用配置模块
"""

from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    APP_NAME: str = "临时邮箱服务"
    APP_VERSION: str = "2.0.0"
    APP_DESCRIPTION: str = "基于FastAPI + POP3协议的临时邮箱服务"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = "mysql+aiomysql://root:@lizesheng123@@************:3306/email"
    DATABASE_HOST: str = "************"
    DATABASE_PORT: int = 3306
    DATABASE_USER: str = "root"
    DATABASE_PASSWORD: str = "@lizesheng123@"
    DATABASE_NAME: str = "email"
    DATABASE_ECHO: bool = False
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production-very-long-and-random"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 * 24 * 60  # 30天
    
    # POP3配置
    POP3_HOST: str = "pop.qq.com"
    POP3_PORT: int = 995
    POP3_USERNAME: str = "<EMAIL>"
    POP3_PASSWORD: str = "nxlfhrqjwutwbdbj"
    POP3_USE_SSL: bool = True
    
    # 性能优化配置
    DEFAULT_HOURS_FILTER: int = 24  # 默认只获取24小时内的邮件
    MAX_EMAILS_LIMIT: int = 100     # 最大邮件数量限制
    EMAIL_CHECK_INTERVAL: int = 60  # 邮件检查间隔（秒）
    
    # 邮箱服务配置 - 使用QQ邮箱SMTP
    SMTP_HOST: str = "smtp.qq.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = "<EMAIL>"
    SMTP_PASSWORD: str = "umpazhxrriatddef"  # QQ邮箱授权码
    SMTP_USE_TLS: bool = True
    
    # 域名配置
    ALLOWED_DOMAINS: List[str] = [
        "32hgh48dfd.shop",  # 主域名
        "tempmail.com", "10minutemail.com", "guerrillamail.com",
        "mailinator.com", "yopmail.com", "temp-mail.org",
        "throwaway.email", "maildrop.cc", "getairmail.com",
        "tempmail.ninja", "fakemailgenerator.com", "mohmal.com"
    ]
    
    # 邮箱生成配置
    MAILBOX_PREFIX_LENGTH: int = 8
    MAILBOX_DOMAIN_ROTATION: bool = True
    
    # 安全配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000", 
        "http://127.0.0.1:3000",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    ]
    
    # 邮件存储配置
    EMAIL_RETENTION_DAYS: int = 7
    MAX_EMAILS_PER_MAILBOX: int = 100
    
    # 限制配置
    DAILY_MAILBOX_LIMIT: int = 10
    DAILY_EMAIL_LIMIT: int = 100
    
    # 垃圾邮件过滤
    ENABLE_SPAM_FILTER: bool = True
    SPAM_SCORE_THRESHOLD: float = 5.0
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # 忽略额外字段


# 全局配置实例
settings = Settings()
