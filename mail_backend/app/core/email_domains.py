"""
允许注册的邮箱域名配置
"""

from typing import Set, Dict, List

# 允许注册的邮箱域名
ALLOWED_EMAIL_DOMAINS: Set[str] = {
    # 国内常用邮箱
    '163.com', '126.com', '139.com', 'sina.com', 'sina.cn',
    'sohu.com', 'yeah.net', 'wo.com.cn', '189.cn',
    
    # QQ邮箱系列
    'qq.com', 'foxmail.com', 'vip.qq.com',
    
    # 国外常用邮箱
    'gmail.com', 'outlook.com', 'hotmail.com', 'live.com',
    'yahoo.com', 'yahoo.cn', 'aol.com', 'icloud.com',
    
    # 企业邮箱常见域名
    'aliyun.com', 'alibaba-inc.com', 'tencent.com',
    'baidu.com', 'bytedance.com', 'meituan.com',
    
    # 其他常用邮箱
    'mail.ru', 'yandex.com', 'protonmail.com'
}

# 按类型分组的邮箱域名
EMAIL_DOMAIN_CATEGORIES: Dict[str, List[str]] = {
    "domestic": [
        '163.com', '126.com', '139.com', 'sina.com', 'sina.cn',
        'sohu.com', 'yeah.net', 'wo.com.cn', '189.cn',
        'qq.com', 'foxmail.com', 'vip.qq.com'
    ],
    "international": [
        'gmail.com', 'outlook.com', 'hotmail.com', 'live.com',
        'yahoo.com', 'yahoo.cn', 'aol.com', 'icloud.com',
        'mail.ru', 'yandex.com', 'protonmail.com'
    ],
    "enterprise": [
        'aliyun.com', 'alibaba-inc.com', 'tencent.com',
        'baidu.com', 'bytedance.com', 'meituan.com'
    ]
}

# 邮箱域名描述
DOMAIN_DESCRIPTIONS: Dict[str, str] = {
    # 国内邮箱
    '163.com': '网易163邮箱',
    '126.com': '网易126邮箱',
    '139.com': '中国移动139邮箱',
    'sina.com': '新浪邮箱',
    'sina.cn': '新浪邮箱',
    'sohu.com': '搜狐邮箱',
    'yeah.net': '网易yeah邮箱',
    'wo.com.cn': '中国联通沃邮箱',
    '189.cn': '中国电信189邮箱',
    
    # QQ邮箱
    'qq.com': 'QQ邮箱',
    'foxmail.com': 'Foxmail邮箱',
    'vip.qq.com': 'QQ VIP邮箱',
    
    # 国外邮箱
    'gmail.com': 'Google Gmail',
    'outlook.com': 'Microsoft Outlook',
    'hotmail.com': 'Microsoft Hotmail',
    'live.com': 'Microsoft Live',
    'yahoo.com': 'Yahoo邮箱',
    'yahoo.cn': 'Yahoo中国',
    'aol.com': 'AOL邮箱',
    'icloud.com': 'Apple iCloud',
    'mail.ru': 'Mail.ru邮箱',
    'yandex.com': 'Yandex邮箱',
    'protonmail.com': 'ProtonMail加密邮箱',
    
    # 企业邮箱
    'aliyun.com': '阿里云邮箱',
    'alibaba-inc.com': '阿里巴巴企业邮箱',
    'tencent.com': '腾讯企业邮箱',
    'baidu.com': '百度企业邮箱',
    'bytedance.com': '字节跳动企业邮箱',
    'meituan.com': '美团企业邮箱'
}


def get_allowed_domains() -> Set[str]:
    """获取允许的邮箱域名集合"""
    return ALLOWED_EMAIL_DOMAINS.copy()


def is_domain_allowed(domain: str) -> bool:
    """检查域名是否在允许列表中"""
    return domain.lower() in ALLOWED_EMAIL_DOMAINS


def get_domain_categories() -> Dict[str, List[str]]:
    """获取按类型分组的邮箱域名"""
    return {
        category: [domain for domain in domains if domain in ALLOWED_EMAIL_DOMAINS]
        for category, domains in EMAIL_DOMAIN_CATEGORIES.items()
    }


def get_domain_description(domain: str) -> str:
    """获取域名描述"""
    return DOMAIN_DESCRIPTIONS.get(domain.lower(), domain)


def get_popular_domains(limit: int = 10) -> List[str]:
    """获取最受欢迎的邮箱域名"""
    # 按使用频率排序（这里简化为固定顺序）
    popular_order = [
        'qq.com', 'gmail.com', '163.com', '126.com', 'outlook.com',
        'sina.com', 'hotmail.com', 'foxmail.com', 'yahoo.com', '139.com'
    ]
    
    return [domain for domain in popular_order if domain in ALLOWED_EMAIL_DOMAINS][:limit]


def add_domain(domain: str) -> bool:
    """添加新的允许域名（运行时修改）"""
    if domain and '.' in domain:
        ALLOWED_EMAIL_DOMAINS.add(domain.lower())
        return True
    return False


def remove_domain(domain: str) -> bool:
    """移除允许域名（运行时修改）"""
    if domain.lower() in ALLOWED_EMAIL_DOMAINS:
        ALLOWED_EMAIL_DOMAINS.remove(domain.lower())
        return True
    return False
