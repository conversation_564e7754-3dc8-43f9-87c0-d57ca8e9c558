"""
FastAPI应用创建和配置
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

from app.core.config import settings
from app.api.router import api_router
# 移除SQLAlchemy引擎依赖


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logging.info(f"{settings.APP_NAME} v{settings.APP_VERSION} is starting...")

    # Web服务不启动邮件同步任务
    # 邮件同步由独立的Python服务处理
    logging.info("Web服务启动完成，邮件同步由独立服务处理")

    yield

    # 关闭时执行
    logging.info(f"{settings.APP_NAME} is shutting down...")

    # Web服务关闭，邮件同步服务独立运行
    logging.info("Web服务关闭完成")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description=settings.APP_DESCRIPTION,
        lifespan=lifespan,
        debug=settings.DEBUG,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册API路由
    app.include_router(api_router)
    
    # 根路径
    @app.get("/")
    async def root():
        return {
            "message": f"{settings.APP_NAME} API is running",
            "version": settings.APP_VERSION,
            "docs": "/docs" if settings.DEBUG else "Documentation disabled in production"
        }
    
    # 健康检查
    @app.get("/health")
    async def health_check():
        return {
            "status": "healthy",
            "app": settings.APP_NAME,
            "version": settings.APP_VERSION
        }
    
    # Ping端点
    @app.get("/ping")
    def ping():
        return {"message": "pong"}
    
    return app
