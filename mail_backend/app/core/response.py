"""
统一响应格式
"""

from typing import Any, Dict, Optional
from fastapi.responses import JSONResponse


class ApiResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(message: str = "操作成功", data: Any = None) -> Dict[str, Any]:
        """成功响应"""
        response = {
            "code": 0,
            "message": message
        }
        if data is not None:
            response["data"] = data
        return response
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 1, data: Any = None) -> Dict[str, Any]:
        """错误响应"""
        response = {
            "code": code,
            "message": message
        }
        if data is not None:
            response["data"] = data
        return response
    
    @staticmethod
    def success_response(message: str = "操作成功", data: Any = None) -> JSONResponse:
        """成功响应（JSONResponse）"""
        return JSONResponse(
            content=ApiResponse.success(message, data),
            status_code=200
        )
    
    @staticmethod
    def error_response(message: str = "操作失败", code: int = 1, status_code: int = 400, data: Any = None) -> JSONResponse:
        """错误响应（JSONResponse）"""
        return JSONResponse(
            content=ApiResponse.error(message, code, data),
            status_code=status_code
        )
