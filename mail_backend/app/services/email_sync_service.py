"""
邮件同步服务 - 从POP3服务器获取邮件并入库
"""

import asyncio
import poplib
import email.message
import email.parser
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from email.header import decode_header
from email.utils import parsedate_to_datetime

from app.database.connection import get_db_connection
from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailSyncService:
    """邮件同步服务"""
    
    def __init__(self):
        # QQ邮箱POP3配置
        self.pop3_host = "pop.qq.com"  # QQ邮箱POP3服务器
        self.pop3_port = 995  # POP3 SSL端口
        self.pop3_username = "<EMAIL>"  # QQ邮箱账号
        self.pop3_password = "nxlfhrqjwutwbdbj"  # QQ邮箱授权码
        self.is_running = False
    
    async def sync_emails_for_mailbox(self, mailbox_id: int, email_address: str) -> Dict[str, Any]:
        """为指定邮箱同步邮件"""
        try:
            # 使用固定的QQ邮箱账号连接POP3服务器
            # 所有临时邮箱的邮件都通过这个QQ邮箱接收
            pop3_conn = poplib.POP3_SSL(self.pop3_host, self.pop3_port)
            pop3_conn.user(self.pop3_username)
            pop3_conn.pass_(self.pop3_password)
            
            # 获取邮件数量
            num_messages = len(pop3_conn.list()[1])
            logger.info(f"QQ邮箱有 {num_messages} 封邮件")

            if num_messages == 0:
                pop3_conn.quit()
                return {"success": True, "message": "没有新邮件", "count": 0}

            # 获取数据库连接
            async with get_db_connection() as conn:
                async with conn.cursor() as cursor:
                    # 获取所有已处理的邮件ID
                    await cursor.execute("SELECT pop3_message_id FROM emails")
                    existing_ids = {row[0] for row in await cursor.fetchall()}

                    # 获取所有邮箱地址映射
                    await cursor.execute("SELECT id, email_address FROM mailboxes WHERE is_active = 1")
                    mailbox_map = {row[1]: row[0] for row in await cursor.fetchall()}

                    new_emails_count = 0

                    # 遍历所有邮件
                    for i in range(1, num_messages + 1):
                        if i in existing_ids:
                            continue  # 跳过已存在的邮件

                        try:
                            # 获取邮件内容
                            raw_email = b'\n'.join(pop3_conn.retr(i)[1])
                            parser = email.parser.BytesParser()
                            msg = parser.parsebytes(raw_email)

                            # 解析收件人地址
                            to_addr = self._decode_header(msg.get('To', ''))

                            # 查找对应的邮箱ID
                            target_mailbox_id = None
                            for mailbox_email, mid in mailbox_map.items():
                                if mailbox_email in to_addr:
                                    target_mailbox_id = mid
                                    break

                            if target_mailbox_id:
                                # 解析邮件并插入对应邮箱
                                email_data = self._parse_email(msg, target_mailbox_id, i)
                                await self._insert_email(cursor, email_data)
                                new_emails_count += 1
                                logger.info(f"邮件分配到邮箱 {mailbox_email} (ID: {target_mailbox_id})")
                            else:
                                logger.warning(f"邮件收件人 {to_addr} 没有匹配的邮箱")

                        except Exception as e:
                            logger.error(f"处理邮件 {i} 失败: {e}")
                            continue

                    await conn.commit()
                    
            pop3_conn.quit()
            
            return {
                "success": True,
                "message": f"同步完成，新增 {new_emails_count} 封邮件",
                "count": new_emails_count
            }
            
        except Exception as e:
            logger.error(f"同步邮箱 {email_address} 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _parse_email(self, msg: email.message.Message, mailbox_id: int, pop3_id: int) -> Dict[str, Any]:
        """解析邮件内容"""
        # 解析主题
        subject = self._decode_header(msg.get('Subject', ''))
        
        # 解析发件人和收件人
        from_addr = self._decode_header(msg.get('From', ''))
        to_addr = self._decode_header(msg.get('To', ''))
        cc_addr = self._decode_header(msg.get('Cc', ''))
        
        # 解析日期
        date_str = msg.get('Date')
        email_date = None
        if date_str:
            try:
                email_date = parsedate_to_datetime(date_str)
            except:
                pass
        
        # 解析邮件内容
        content_text = ""
        content_html = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    content_text = self._get_payload(part)
                elif content_type == "text/html":
                    content_html = self._get_payload(part)
        else:
            content_type = msg.get_content_type()
            if content_type == "text/plain":
                content_text = self._get_payload(msg)
            elif content_type == "text/html":
                content_html = self._get_payload(msg)
        
        return {
            'mailbox_id': mailbox_id,
            'pop3_message_id': pop3_id,
            'message_id': msg.get('Message-ID', ''),
            'subject': subject,
            'from_addr': from_addr,
            'to_addr': to_addr,
            'cc_addr': cc_addr,
            'content_text': content_text,
            'content_html': content_html,
            'email_date': email_date,
            'received_at': datetime.now(),
            'raw_headers': str(msg)
        }
    
    def _decode_header(self, header: str) -> str:
        """解码邮件头"""
        if not header:
            return ""
        
        try:
            decoded_parts = decode_header(header)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            return result
        except:
            return header
    
    def _get_payload(self, part) -> str:
        """获取邮件内容"""
        try:
            payload = part.get_payload(decode=True)
            if isinstance(payload, bytes):
                charset = part.get_content_charset() or 'utf-8'
                return payload.decode(charset, errors='ignore')
            return str(payload)
        except:
            return ""
    
    async def _insert_email(self, cursor, email_data: Dict[str, Any]):
        """插入邮件到数据库"""
        await cursor.execute("""
            INSERT INTO emails (
                mailbox_id, pop3_message_id, message_id, subject, from_addr, to_addr, cc_addr,
                content_text, content_html, email_date, received_at, raw_headers
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            email_data['mailbox_id'],
            email_data['pop3_message_id'],
            email_data['message_id'],
            email_data['subject'],
            email_data['from_addr'],
            email_data['to_addr'],
            email_data['cc_addr'],
            email_data['content_text'],
            email_data['content_html'],
            email_data['email_date'],
            email_data['received_at'],
            email_data['raw_headers']
        ))
    
    async def sync_all_active_mailboxes(self):
        """同步所有活跃邮箱的邮件 - 从QQ邮箱获取并分发到对应邮箱"""
        try:
            # 直接调用同步方法，传入任意邮箱ID（实际不使用）
            result = await self.sync_emails_for_mailbox(0, "<EMAIL>")
            if result["success"]:
                logger.info(f"邮件同步完成: {result['message']}")
            else:
                logger.error(f"邮件同步失败: {result['error']}")

        except Exception as e:
            logger.error(f"同步所有邮箱失败: {e}")


# 创建全局实例
email_sync_service = EmailSyncService()
