"""
邮箱管理服务
"""

import secrets
import string
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

# 移除SQLAlchemy模型依赖


class MailboxService:
    """邮箱管理服务"""
    
    @staticmethod
    def generate_random_email(domain: str) -> str:
        """生成随机邮箱地址"""
        # 生成8位随机字符串
        chars = string.ascii_lowercase + string.digits
        random_part = ''.join(secrets.choice(chars) for _ in range(8))
        return f"{random_part}@{domain}"
    
    @staticmethod
    async def allocate_mailbox(
        db: AsyncSession,
        user_id: int
    ) -> Dict[str, Any]:
        """系统分配新邮箱给用户"""
        try:
            # 获取用户信息
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                return {"success": False, "message": "用户不存在"}
            
            # 检查今天已创建的邮箱数量
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            used_today = await user.get_mailboxes_created_today(db, today_start)
            
            if used_today >= user.daily_mailbox_limit:
                return {
                    "success": False,
                    "message": f"今日邮箱分配数量已达上限({user.daily_mailbox_limit}个)"
                }
            
            # 获取可用域名
            domain_result = await db.execute(
                select(Domain).where(Domain.is_active == True).limit(1)
            )
            domain = domain_result.scalar_one_or_none()
            
            if not domain:
                return {"success": False, "message": "暂无可用域名"}
            
            # 生成邮箱地址（确保唯一性）
            max_attempts = 10
            for _ in range(max_attempts):
                email_address = MailboxService.generate_random_email(domain.domain_name)
                
                # 检查邮箱是否已存在
                existing_result = await db.execute(
                    select(Mailbox).where(Mailbox.email_address == email_address)
                )
                if not existing_result.scalar_one_or_none():
                    break
            else:
                return {"success": False, "message": "生成邮箱地址失败，请重试"}

            # 系统分配邮箱给用户
            mailbox = Mailbox(
                user_id=user_id,
                email_address=email_address,
                display_name=f"临时邮箱 {email_address.split('@')[0]}",
                domain_id=domain.id,
                is_active=True
            )

            db.add(mailbox)
            await db.commit()
            await db.refresh(mailbox)

            return {
                "success": True,
                "message": "系统已为您分配新邮箱",
                "mailbox": {
                    "id": mailbox.id,
                    "email_address": mailbox.email_address,
                    "display_name": mailbox.display_name,
                    "is_active": mailbox.is_active,
                    "created_at": mailbox.created_at.isoformat(),
                    "total_emails": 0,
                    "unread_emails": 0
                },
                "remaining_mailboxes_today": max(0, user.daily_mailbox_limit - used_today - 1)
            }
            
        except Exception as e:
            await db.rollback()
            return {"success": False, "message": f"分配邮箱失败: {str(e)}"}
    
    @staticmethod
    async def get_temp_mailbox_for_guest(
        db: AsyncSession,
        client_ip: str
    ) -> Dict[str, Any]:
        """未注册用户获取临时邮箱（IP限制：每天1个）"""
        try:
            # 检查今天该IP是否已经获取过临时邮箱
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)

            # 查询今天该IP创建的临时邮箱数量（user_id为NULL的邮箱）
            ip_mailboxes_today = await db.execute(
                select(func.count(Mailbox.id)).where(
                    and_(
                        Mailbox.user_id == None,
                        Mailbox.created_at >= today_start,
                        Mailbox.client_ip == client_ip  # 需要在Mailbox模型中添加client_ip字段
                    )
                )
            )
            ip_count = ip_mailboxes_today.scalar() or 0

            if ip_count >= 1:
                return {
                    "success": False,
                    "message": "该IP今日已获取临时邮箱，每天限制1个"
                }

            # 获取可用域名
            domain_result = await db.execute(
                select(Domain).where(Domain.is_active == True).limit(1)
            )
            domain = domain_result.scalar_one_or_none()

            if not domain:
                return {"success": False, "message": "暂无可用域名"}

            # 生成邮箱地址（确保唯一性）
            max_attempts = 10
            for _ in range(max_attempts):
                email_address = MailboxService.generate_random_email(domain.domain_name)

                # 检查邮箱是否已存在
                existing_result = await db.execute(
                    select(Mailbox).where(Mailbox.email_address == email_address)
                )
                if not existing_result.scalar_one_or_none():
                    break
            else:
                return {"success": False, "message": "生成邮箱地址失败，请重试"}

            # 创建临时邮箱（不绑定用户）
            mailbox = Mailbox(
                user_id=None,  # 临时邮箱不绑定用户
                email_address=email_address,
                display_name=f"临时邮箱 {email_address.split('@')[0]}",
                domain_id=domain.id,
                client_ip=client_ip,  # 记录IP地址
                is_active=True
            )

            db.add(mailbox)
            await db.commit()
            await db.refresh(mailbox)

            return {
                "success": True,
                "message": "临时邮箱获取成功（24小时有效）",
                "mailbox": {
                    "id": mailbox.id,
                    "email_address": mailbox.email_address,
                    "display_name": mailbox.display_name,
                    "is_active": mailbox.is_active,
                    "created_at": mailbox.created_at.isoformat(),
                    "expires_at": (mailbox.created_at + timedelta(hours=24)).isoformat(),
                    "total_emails": 0,
                    "unread_emails": 0
                },
                "note": "临时邮箱24小时后自动失效，注册账号可获得更多邮箱"
            }

        except Exception as e:
            await db.rollback()
            return {"success": False, "message": f"获取临时邮箱失败: {str(e)}"}

    @staticmethod
    async def auto_allocate_initial_mailboxes(
        db: AsyncSession,
        user_id: int,
        count: int = 3
    ) -> List[Dict[str, Any]]:
        """用户注册后自动分配初始邮箱"""
        allocated_mailboxes = []

        for i in range(count):
            result = await MailboxService.allocate_mailbox(db, user_id)
            if result["success"]:
                allocated_mailboxes.append(result["mailbox"])
            else:
                break  # 如果分配失败就停止

        return allocated_mailboxes

    @staticmethod
    async def get_user_mailboxes(
        db: AsyncSession,
        user_id: int,
        skip: int = 0,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取用户的邮箱列表"""
        try:
            result = await db.execute(
                select(Mailbox)
                .where(Mailbox.user_id == user_id)
                .order_by(Mailbox.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            mailboxes = result.scalars().all()
            
            mailbox_list = []
            for mailbox in mailboxes:
                # 获取邮箱的邮件统计
                email_count = await mailbox.get_email_count(db)
                unread_count = await mailbox.get_unread_email_count(db)
                
                mailbox_list.append({
                    "id": mailbox.id,
                    "email_address": mailbox.email_address,
                    "display_name": mailbox.display_name,
                    "is_active": mailbox.is_active,
                    "created_at": mailbox.created_at.isoformat(),
                    "total_emails": email_count,
                    "unread_emails": unread_count
                })
            
            return mailbox_list
            
        except Exception as e:
            return []
    
    @staticmethod
    async def delete_mailbox(
        db: AsyncSession,
        user_id: int,
        mailbox_id: int
    ) -> Dict[str, Any]:
        """删除邮箱"""
        try:
            result = await db.execute(
                select(Mailbox).where(
                    and_(
                        Mailbox.id == mailbox_id,
                        Mailbox.user_id == user_id
                    )
                )
            )
            mailbox = result.scalar_one_or_none()
            
            if not mailbox:
                return {"success": False, "message": "邮箱不存在或无权限"}
            
            # 软删除（标记为不活跃）
            mailbox.is_active = False
            mailbox.deleted_at = datetime.utcnow()
            
            await db.commit()
            
            return {"success": True, "message": "邮箱删除成功"}
            
        except Exception as e:
            await db.rollback()
            return {"success": False, "message": f"删除邮箱失败: {str(e)}"}
    
    @staticmethod
    async def get_mailbox_stats(
        db: AsyncSession,
        user_id: int
    ) -> Dict[str, Any]:
        """获取用户邮箱统计信息"""
        try:
            # 总邮箱数
            total_result = await db.execute(
                select(func.count(Mailbox.id)).where(Mailbox.user_id == user_id)
            )
            total_mailboxes = total_result.scalar() or 0
            
            # 活跃邮箱数
            active_result = await db.execute(
                select(func.count(Mailbox.id)).where(
                    and_(
                        Mailbox.user_id == user_id,
                        Mailbox.is_active == True
                    )
                )
            )
            active_mailboxes = active_result.scalar() or 0
            
            # 今天创建的邮箱数
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today_result = await db.execute(
                select(func.count(Mailbox.id)).where(
                    and_(
                        Mailbox.user_id == user_id,
                        Mailbox.created_at >= today_start
                    )
                )
            )
            today_created = today_result.scalar() or 0
            
            return {
                "total_mailboxes": total_mailboxes,
                "active_mailboxes": active_mailboxes,
                "today_created": today_created
            }
            
        except Exception as e:
            return {
                "total_mailboxes": 0,
                "active_mailboxes": 0,
                "today_created": 0
            }


# 全局邮箱服务实例
mailbox_service = MailboxService()
