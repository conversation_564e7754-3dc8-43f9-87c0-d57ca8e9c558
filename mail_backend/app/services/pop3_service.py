"""
POP3邮件服务
重构后的POP3邮件获取服务
"""

import poplib
import email
import email.message
import logging
from datetime import datetime, timedelta, timezone
from email.utils import parsedate_to_datetime
from typing import List, Dict, Any, Optional
# 移除SQLAlchemy依赖

from app.core.config import settings
# 移除SQLAlchemy模型依赖
# from app.services.email.email_parser import EmailParser

logger = logging.getLogger(__name__)


class POP3Service:
    """POP3邮件服务类"""
    
    def __init__(self):
        self.pop3_server = settings.POP3_HOST
        self.pop3_port = settings.POP3_PORT
        self.username = settings.POP3_USERNAME
        self.password = settings.POP3_PASSWORD
        self.use_ssl = settings.POP3_USE_SSL
        
        # 性能优化配置
        self.default_hours_filter = settings.DEFAULT_HOURS_FILTER
        self.max_emails_limit = settings.MAX_EMAILS_LIMIT
        
        # self.email_parser = EmailParser()
    
    async def connect_pop3(self) -> poplib.POP3_SSL:
        """连接到POP3服务器"""
        try:
            if self.use_ssl:
                server = poplib.POP3_SSL(self.pop3_server, self.pop3_port)
            else:
                server = poplib.POP3(self.pop3_server, self.pop3_port)
            
            server.user(self.username)
            server.pass_(self.password)
            
            logger.info(f"📧 成功连接到POP3服务器: {self.pop3_server}:{self.pop3_port}")
            return server
            
        except Exception as e:
            logger.error(f"❌ POP3连接失败: {e}")
            raise
    
    async def get_emails(
        self,
        hours: Optional[int] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        获取邮件列表
        
        Args:
            hours: 时间过滤（小时）
            limit: 邮件数量限制
            db: 数据库会话（可选，用于存储邮件）
        
        Returns:
            邮件列表
        """
        if hours is None:
            hours = self.default_hours_filter
        
        logger.info(f"📧 获取最近 {hours} 小时内的 {limit} 封邮件")
        
        try:
            server = await self.connect_pop3()
            
            # 获取邮件数量
            num_messages = len(server.list()[1])
            logger.info(f"📧 服务器中有 {num_messages} 封邮件，筛选最近 {hours} 小时内的邮件")
            
            if num_messages == 0:
                server.quit()
                return []
            
            # 计算时间阈值
            time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            emails = []
            processed_count = 0
            
            # 从最新邮件开始处理（倒序）
            for i in range(num_messages, 0, -1):
                if len(emails) >= limit:
                    break
                
                try:
                    processed_count += 1
                    
                    # 获取完整邮件内容
                    raw_email = b"\n".join(server.retr(i)[1])
                    email_message = email.message_from_bytes(raw_email)
                    
                    # 检查邮件时间
                    date_str = email_message.get("Date", "")
                    email_date = None
                    if date_str:
                        try:
                            email_date = parsedate_to_datetime(date_str)
                            # 确保时间都是aware的
                            if email_date.tzinfo is None:
                                email_date = email_date.replace(tzinfo=timezone.utc)
                            if time_threshold.tzinfo is None:
                                time_threshold = time_threshold.replace(tzinfo=timezone.utc)
                            
                            # 如果邮件时间早于阈值，跳过
                            if email_date < time_threshold:
                                if processed_count > 10:  # 检查最近10封邮件
                                    logger.info(f"📧 邮件时间过早，停止检查。最后检查的邮件时间: {email_date}")
                                    break
                                continue
                        except Exception as e:
                            logger.warning(f"解析邮件时间失败: {e}")
                    
                    # 解析邮件
                    parsed_email = self._parse_email_simple(email_message, i)
                    emails.append(parsed_email)
                    
                except Exception as e:
                    logger.error(f"处理邮件 {i} 时出错: {e}")
                    continue
            
            server.quit()
            
            logger.info(f"📧 成功获取 {len(emails)} 封邮件（最近 {hours} 小时内，处理了 {processed_count} 封邮件）")
            return emails
            
        except Exception as e:
            logger.error(f"❌ 获取邮件失败: {e}")
            raise
    
    async def get_recent_emails(self, hours: int = 1, limit: int = 20) -> List[Dict[str, Any]]:
        """快速获取最近邮件"""
        logger.info(f"📧 快速获取最近 {hours} 小时内的 {limit} 封邮件")
        return await self.get_emails(hours=hours, limit=limit)
    
    async def get_today_emails(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取今天的邮件"""
        logger.info(f"📧 获取今天的 {limit} 封邮件")
        return await self.get_emails(hours=24, limit=limit)

    def _parse_email_simple(self, email_message, message_id: int) -> Dict[str, Any]:
        """简单邮件解析"""
        try:
            # 基本信息
            subject = email_message.get("Subject", "")
            from_addr = email_message.get("From", "")
            to_addr = email_message.get("To", "")
            cc_addr = email_message.get("Cc", "")
            reply_to = email_message.get("Reply-To", "")
            message_id_header = email_message.get("Message-ID", "")
            date_str = email_message.get("Date", "")

            # 解析日期
            email_date = None
            if date_str:
                try:
                    email_date = parsedate_to_datetime(date_str)
                    if email_date.tzinfo is None:
                        email_date = email_date.replace(tzinfo=timezone.utc)
                except Exception:
                    pass

            # 获取邮件内容
            content_text = ""
            content_html = ""

            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        try:
                            content_text = part.get_content()
                        except Exception:
                            content_text = str(part.get_payload(decode=True), 'utf-8', errors='ignore')
                    elif content_type == "text/html":
                        try:
                            content_html = part.get_content()
                        except Exception:
                            content_html = str(part.get_payload(decode=True), 'utf-8', errors='ignore')
            else:
                try:
                    content = email_message.get_content()
                    if email_message.get_content_type() == "text/html":
                        content_html = content
                    else:
                        content_text = content
                except Exception:
                    content_text = str(email_message.get_payload(decode=True), 'utf-8', errors='ignore')

            # 检查是否为转发邮件
            is_forwarded = False
            original_from = None
            original_to = None

            if "转发" in subject or "Fwd:" in subject or "FW:" in subject:
                is_forwarded = True
                # 简单的转发邮件解析
                if content_text:
                    lines = content_text.split('\n')
                    for line in lines:
                        if line.startswith('From:') or line.startswith('发件人:'):
                            original_from = line.split(':', 1)[1].strip()
                        elif line.startswith('To:') or line.startswith('收件人:'):
                            original_to = line.split(':', 1)[1].strip()

            return {
                'id': message_id,
                'message_id': message_id_header,
                'subject': subject,
                'from_addr': from_addr,
                'to_addr': to_addr,
                'cc_addr': cc_addr,
                'reply_to': reply_to,
                'content_text': content_text,
                'content_html': content_html,
                'date': email_date,
                'is_forwarded': is_forwarded,
                'original_from': original_from,
                'original_to': original_to,
                'size': len(str(email_message)),
                'has_attachments': False,  # 简化版本不处理附件
                'raw_headers': str(email_message)
            }

        except Exception as e:
            logger.error(f"解析邮件失败: {e}")
            return {
                'id': message_id,
                'subject': "解析失败",
                'from_addr': "unknown",
                'to_addr': "unknown",
                'content_text': f"邮件解析失败: {e}",
                'date': datetime.now(timezone.utc),
                'is_forwarded': False
            }
    
    # 数据库保存功能暂时移除


# 全局POP3服务实例
pop3_service = POP3Service()
