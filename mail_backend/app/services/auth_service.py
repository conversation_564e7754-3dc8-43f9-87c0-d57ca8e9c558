"""
用户认证服务
"""

import hashlib
import secrets
import random
import smtplib
import re
from email.mime.text import MIMEText
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Set

# 内存存储验证码 {email: {"code": "123456", "expires_at": datetime}}
verification_codes: Dict[str, Dict[str, Any]] = {}
# 移除SQLAlchemy依赖，使用aiomysql直连, func, and_
import jwt

from app.core.config import settings
from app.core.email_domains import get_allowed_domains, is_domain_allowed, get_domain_categories
# 移除SQLAlchemy模型依赖


def cleanup_expired_codes():
    """清理过期的验证码"""
    now = datetime.now()
    expired_emails = [email for email, data in verification_codes.items()
                     if now > data["expires_at"]]
    for email in expired_emails:
        del verification_codes[email]
    if expired_emails:
        print(f"🧹 清理了 {len(expired_emails)} 个过期验证码")

class AuthService:
    """认证服务"""



    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()

    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """验证密码"""
        return hashlib.sha256(password.encode()).hexdigest() == hashed

    @staticmethod
    def create_access_token(user_id: int) -> str:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        payload = {
            "user_id": user_id,
            "exp": expire
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    @staticmethod
    def is_allowed_email_domain(email: str) -> bool:
        """检查邮箱域名是否在允许列表中"""
        if not AuthService.is_valid_email(email):
            return False

        domain = email.split('@')[1].lower()
        return is_domain_allowed(domain)

    @staticmethod
    def get_allowed_domains() -> Set[str]:
        """获取允许的邮箱域名列表"""
        return get_allowed_domains()

    @staticmethod
    def generate_verification_code() -> str:
        """生成6位数字验证码"""
        return str(random.randint(100000, 999999))

    @staticmethod
    async def send_verification_email(email: str, code: str) -> Dict[str, Any]:
        """发送验证码邮件"""
        try:
            from app.core.config import settings

            # 创建邮件内容
            subject = "邮箱验证码"
            body = f"""
            您好！

            您的验证码是：{code}

            验证码有效期为10分钟，请及时使用。

            如果这不是您的操作，请忽略此邮件。

            临时邮箱服务
            """

            # 创建邮件对象
            msg = MIMEText(body, 'plain', 'utf-8')
            msg['Subject'] = subject
            msg['From'] = settings.SMTP_USERNAME
            msg['To'] = email

            # 发送邮件
            server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
            server.starttls()  # 启用TLS加密
            server.login(settings.SMTP_USERNAME, settings.SMTP_PASSWORD)
            server.send_message(msg)
            server.quit()

            print(f"✅ 验证码邮件已发送到 {email}: {code}")
            return {"success": True, "message": "邮件发送成功"}

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 发送邮件失败: {e}")

            # 解析具体的错误信息
            if "550" in error_msg and "non-existent account" in error_msg:
                return {"success": False, "message": f"邮箱地址不存在或无效: {email}"}
            elif "535" in error_msg:
                return {"success": False, "message": "SMTP认证失败，请检查邮箱配置"}
            elif "timeout" in error_msg.lower():
                return {"success": False, "message": "邮件服务器连接超时，请稍后重试"}
            else:
                return {"success": False, "message": f"邮件发送失败: {error_msg}"}
    
    @staticmethod
    async def send_verification_code(
        email: str
    ) -> Dict[str, Any]:
        """发送邮箱验证码"""
        from app.database.connection import get_db_connection

        try:
            from app.core.response import ApiResponse

            # 验证邮箱格式
            if not AuthService.is_valid_email(email):
                return ApiResponse.error("邮箱格式不正确")

            # 检查邮箱域名是否允许
            if not AuthService.is_allowed_email_domain(email):
                allowed_domains = ', '.join(sorted(list(get_allowed_domains())[:10]))  # 显示前10个
                return ApiResponse.error(f"不支持该邮箱域名，请使用常用邮箱注册（如：{allowed_domains}等）")

            # 生成验证码
            code = AuthService.generate_verification_code()

            # 检查邮箱是否已注册
            async with get_db_connection() as connection:
                async with connection.cursor() as cursor:
                    await cursor.execute("SELECT id FROM users WHERE email = %s", (email,))
                    existing_user = await cursor.fetchone()
                    if existing_user:
                        return ApiResponse.error("该邮箱已注册")

            # 发送邮件
            email_result = await AuthService.send_verification_email(email, code)
            if email_result["success"]:
                # 邮件发送成功后，保存验证码到内存（临时存储）
                expires_at = datetime.now() + timedelta(minutes=10)
                verification_codes[email] = {
                    "code": code,
                    "expires_at": expires_at
                }
                print(f"✅ 验证码已保存到内存: {email} -> {code} (过期时间: {expires_at})")

                return ApiResponse.success("验证码已发送到您的邮箱，10分钟内有效")
            else:
                # 返回具体的邮件发送错误信息
                return ApiResponse.error(email_result["message"])

        except Exception as e:
            return ApiResponse.error(f"发送验证码失败: {str(e)}")

    @staticmethod
    async def register_user(
        email: str,
        password: str,
        verification_code: str
    ) -> Dict[str, Any]:
        """用户注册（邮箱+密码+验证码）"""
        try:
            from app.database.connection import get_db_connection
            from app.core.response import ApiResponse

            # 基本验证
            if len(password) < 6:
                return ApiResponse.error("密码至少6个字符")

            # 验证邮箱格式
            if not AuthService.is_valid_email(email):
                return ApiResponse.error("邮箱格式不正确")

            # 检查邮箱域名是否允许
            if not AuthService.is_allowed_email_domain(email):
                allowed_domains = ', '.join(sorted(list(get_allowed_domains())[:10]))  # 显示前10个
                return ApiResponse.error(f"不支持该邮箱域名，请使用常用邮箱注册（如：{allowed_domains}等）")

            async with get_db_connection() as connection:
                async with connection.cursor() as cursor:
                    # 检查邮箱是否已注册
                    await cursor.execute("SELECT id FROM users WHERE email = %s", (email,))
                    existing_user = await cursor.fetchone()
                    if existing_user:
                        return ApiResponse.error("该邮箱已注册")

                    # 验证验证码（从内存中验证）
                    if email not in verification_codes:
                        return ApiResponse.error("验证码无效或已过期")

                    stored_data = verification_codes[email]
                    if stored_data["code"] != verification_code:
                        return ApiResponse.error("验证码错误")

                    if datetime.now() > stored_data["expires_at"]:
                        # 清除过期的验证码
                        del verification_codes[email]
                        return ApiResponse.error("验证码已过期")

                    # 验证码正确，清除内存中的验证码
                    del verification_codes[email]
                    print(f"✅ 验证码验证成功，已清除: {email}")

                    # 创建新用户（邮箱作为用户名）
                    hashed_password = AuthService.hash_password(password)
                    await cursor.execute("""
                        INSERT INTO users (username, password_hash, email, daily_mailbox_limit, is_active, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (email, hashed_password, email, 10, True, datetime.now()))

                    user_id = cursor.lastrowid
                    await connection.commit()

                    # 创建访问令牌
                    access_token = AuthService.create_access_token(user_id)

                    return ApiResponse.success("注册成功！", {
                        "user": {
                            "id": user_id,
                            "username": email,
                            "email": email,
                            "daily_mailbox_limit": 10
                        },
                        "access_token": access_token,
                        "token_type": "bearer"
                    })

        except Exception as e:
            return ApiResponse.error(f"注册失败: {str(e)}")
    
    @staticmethod
    async def login_user(
        email: str,
        password: str
    ) -> Dict[str, Any]:
        """用户登录（邮箱+密码）"""
        try:
            # 查找用户（邮箱作为用户名）
            result = await db.execute(select(User).where(User.email == email))
            user = result.scalar_one_or_none()

            if not user:
                return {"success": False, "message": "邮箱未注册"}

            if not user.is_active:
                return {"success": False, "message": "账户已被禁用"}

            # 验证密码
            if not AuthService.verify_password(password, user.password_hash):
                return {"success": False, "message": "密码错误"}

            # 更新最后登录时间
            user.last_login_at = datetime.utcnow()
            await db.commit()

            # 创建访问令牌
            access_token = AuthService.create_access_token(user.id)

            # 计算今天已使用的邮箱数量
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            used_today = await user.get_mailboxes_created_today(db, today_start)

            return {
                "success": True,
                "message": "登录成功",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "daily_mailbox_limit": user.daily_mailbox_limit,
                    "used_mailboxes_today": used_today,
                    "remaining_mailboxes_today": max(0, user.daily_mailbox_limit - used_today)
                },
                "access_token": access_token,
                "token_type": "bearer"
            }

        except Exception as e:
            return {"success": False, "message": f"登录失败: {str(e)}"}
    
    # 其他方法暂时移除，专注于验证码和注册功能


# 全局认证服务实例
auth_service = AuthService()
