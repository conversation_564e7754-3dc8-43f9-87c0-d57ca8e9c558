"""
授权码管理服务
"""

import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.models.auth_code import AuthCode, AuthCodeType
from app.models.user import User


class AuthCodeService:
    """授权码管理服务"""
    
    @staticmethod
    def generate_auth_code(length: int = 16) -> str:
        """生成授权码"""
        # 使用大写字母和数字，避免容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @staticmethod
    async def create_mailbox_auth_codes(
        db: AsyncSession,
        code_type: AuthCodeType,
        count: int,
        admin_user_id: Optional[int] = None,
        expires_days: int = 365
    ) -> Dict[str, Any]:
        """批量创建邮箱授权码"""
        try:
            if code_type == AuthCodeType.EMAIL_VERIFICATION:
                return {"success": False, "message": "不能创建邮箱验证类型的授权码"}
            
            quota = AuthCode.get_quota_by_type(code_type)
            if quota == 0:
                return {"success": False, "message": "无效的授权码类型"}
            
            created_codes = []
            expires_at = datetime.utcnow() + timedelta(days=expires_days)
            
            for _ in range(count):
                # 生成唯一授权码
                max_attempts = 10
                for _ in range(max_attempts):
                    code = AuthCodeService.generate_auth_code()
                    
                    # 检查是否已存在
                    existing = await db.execute(
                        select(AuthCode).where(AuthCode.code == code)
                    )
                    if not existing.scalar_one_or_none():
                        break
                else:
                    return {"success": False, "message": "生成唯一授权码失败"}
                
                # 创建授权码
                auth_code = AuthCode(
                    code=code,
                    code_type=code_type,
                    mailbox_quota=quota,
                    expires_at=expires_at,
                    created_by_admin_id=admin_user_id
                )
                
                db.add(auth_code)
                created_codes.append({
                    "code": code,
                    "type": code_type.value,
                    "quota": quota,
                    "expires_at": expires_at.isoformat()
                })
            
            await db.commit()
            
            return {
                "success": True,
                "message": f"成功创建{count}个{quota}邮箱授权码",
                "codes": created_codes,
                "total_count": count,
                "code_type": code_type.value,
                "quota_per_code": quota
            }
            
        except Exception as e:
            await db.rollback()
            return {"success": False, "message": f"创建授权码失败: {str(e)}"}
    
    @staticmethod
    async def use_mailbox_auth_code(
        db: AsyncSession,
        code: str,
        user_id: int
    ) -> Dict[str, Any]:
        """使用邮箱授权码"""
        try:
            # 查找授权码
            result = await db.execute(
                select(AuthCode).where(
                    and_(
                        AuthCode.code == code,
                        AuthCode.is_used == False,
                        AuthCode.expires_at > datetime.utcnow()
                    )
                )
            )
            auth_code = result.scalar_one_or_none()
            
            if not auth_code:
                return {"success": False, "message": "授权码无效或已过期"}
            
            if not auth_code.is_mailbox_code:
                return {"success": False, "message": "此授权码不是邮箱授权码"}
            
            # 检查用户是否存在
            user_result = await db.execute(select(User).where(User.id == user_id))
            user = user_result.scalar_one_or_none()
            
            if not user:
                return {"success": False, "message": "用户不存在"}
            
            # 标记授权码为已使用
            auth_code.is_used = True
            auth_code.used_at = datetime.utcnow()
            auth_code.used_by_user_id = user_id
            
            # 增加用户的邮箱配额
            user.daily_mailbox_limit += auth_code.mailbox_quota
            
            await db.commit()
            
            return {
                "success": True,
                "message": f"授权码使用成功！获得{auth_code.mailbox_quota}个邮箱配额",
                "quota_added": auth_code.mailbox_quota,
                "new_daily_limit": user.daily_mailbox_limit,
                "code_type": auth_code.code_type.value
            }
            
        except Exception as e:
            await db.rollback()
            return {"success": False, "message": f"使用授权码失败: {str(e)}"}
    
    @staticmethod
    async def get_auth_code_stats(db: AsyncSession) -> Dict[str, Any]:
        """获取授权码统计信息"""
        try:
            stats = {}
            
            for code_type in AuthCodeType:
                if code_type == AuthCodeType.EMAIL_VERIFICATION:
                    continue
                
                # 统计各类型授权码
                total_result = await db.execute(
                    select(func.count(AuthCode.id)).where(AuthCode.code_type == code_type)
                )
                total = total_result.scalar() or 0
                
                used_result = await db.execute(
                    select(func.count(AuthCode.id)).where(
                        and_(AuthCode.code_type == code_type, AuthCode.is_used == True)
                    )
                )
                used = used_result.scalar() or 0
                
                stats[code_type.value] = {
                    "total": total,
                    "used": used,
                    "available": total - used,
                    "quota_per_code": AuthCode.get_quota_by_type(code_type)
                }
            
            return {
                "success": True,
                "stats": stats
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取统计失败: {str(e)}"}
