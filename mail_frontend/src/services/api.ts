import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api/v1',  // 开发和生产环境都使用相对路径，通过代理转发
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加授权头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('isAuthenticated')
      window.location.href = '/login'
    }

    // 处理HTTP错误状态码
    const errorMessage = error.response?.data?.message || error.response?.data?.detail || '网络错误，请稍后重试'
    return Promise.reject({
      response: {
        data: {
          success: false,
          message: errorMessage,
          data: null
        }
      }
    })
  }
)

// API 接口定义
export const authAPI = {
  // 发送邮箱验证码
  sendCode: (email: string) => api.post('/auth/send-code', { email }),

  // 用户注册
  register: (email: string, password: string, verification_code: string) =>
    api.post('/auth/register', { email, password, verification_code }),

  // 用户登录
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),

  // 获取允许的邮箱域名
  getAllowedDomains: () => api.get('/auth/allowed-domains')
}

export const mailboxAPI = {
  // 获取临时邮箱（未注册用户）
  getTempMailbox: () => api.post('/mailboxes/temp'),

  // 申请邮箱（注册用户）
  allocateMailbox: () => api.post('/mailboxes/allocate'),

  // 获取用户邮箱列表
  getMailboxes: () => api.get('/mailboxes/'),

  // 删除邮箱
  deleteMailbox: (id: number) => api.delete(`/mailboxes/${id}`),

  // 获取邮箱统计
  getStats: () => api.get('/mailboxes/stats')
}

export const emailAPI = {
  // 获取最近邮件
  getRecentEmails: (params?: any) => api.get('/emails/recent', { params }),

  // 获取今天的邮件
  getTodayEmails: (params?: any) => api.get('/emails/today', { params }),

  // 获取邮件详情
  getEmail: (id: number) => api.get(`/emails/${id}`),

  // 标记邮件为已读
  markAsRead: (id: number) => api.put(`/emails/${id}/read`),

  // 删除邮件
  deleteEmail: (id: number) => api.delete(`/emails/${id}`)
}

export const authCodeAPI = {
  // 使用邮箱授权码
  useAuthCode: (code: string) => api.post('/auth-codes/use', { code }),

  // 创建授权码（管理员）
  createAuthCodes: (code_type: string, count: number, expires_days: number = 365) =>
    api.post('/auth-codes/create', { code_type, count, expires_days }),

  // 获取授权码统计（管理员）
  getStats: () => api.get('/auth-codes/stats'),

  // 获取授权码类型
  getTypes: () => api.get('/auth-codes/types')
}

export const userAPI = {
  // 获取用户信息
  getProfile: () => api.get('/users/profile'),

  // 更新用户信息
  updateProfile: (data: any) => api.patch('/users/profile', data),

  // 获取用户统计
  getStats: () => api.get('/users/stats')
}

export default api
