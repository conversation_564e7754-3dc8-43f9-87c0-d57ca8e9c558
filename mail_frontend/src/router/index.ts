import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import Dashboard from '@/views/Dashboard.vue'
import Login from '@/views/Login.vue'
import MailboxList from '@/views/MailboxList.vue'
import MailboxDetail from '@/views/MailboxDetail.vue'
import ForwardingSettings from '@/views/ForwardingSettings.vue'
import DomainManagement from '@/views/DomainManagement.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login
    },
    {
      path: '/',
      name: 'dashboard',
      component: Dashboard
    },
    {
      path: '/mailboxes',
      name: 'mailboxes',
      component: MailboxList
    },
    {
      path: '/mailbox/:id',
      name: 'mailbox-detail',
      component: MailboxDetail,
      meta: { requiresAuth: true }
    },
    {
      path: '/forwarding',
      name: 'forwarding',
      component: ForwardingSettings,
      meta: { requiresAuth: true }
    },
    {
      path: '/domains',
      name: 'domains',
      component: DomainManagement,
      meta: { requiresAuth: true, requiresAdmin: true }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 检查本地存储的认证状态
  if (!userStore.isAuthenticated) {
    userStore.checkAuth()
  }

  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresAdmin && (!userStore.isAuthenticated || !userStore.user?.isAdmin)) {
    next('/')
  } else if (to.path === '/login' && userStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
