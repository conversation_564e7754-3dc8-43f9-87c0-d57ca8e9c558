import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { User, ApiResponse, EmailDomain } from '@/types'
import { authAPI, userAPI } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const allowedDomains = ref<EmailDomain[]>([])
  const verificationCodeSent = ref(false)
  const verificationEmail = ref('')

  const sendVerificationCode = async (email: string) => {
    loading.value = true
    try {
      const response: any = await authAPI.sendCode(email)
      // 处理后端 {code: 0, message: ""} 格式
      if (response.code === 0) {
        verificationCodeSent.value = true
        verificationEmail.value = email
        return { success: true, message: response.message }
      } else {
        return { success: false, error: response.message }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || error.response?.data?.detail || '发送验证码失败，请稍后重试'
      }
    } finally {
      loading.value = false
    }
  }

  const login = async (email: string, password: string) => {
    loading.value = true
    try {
      const response: ApiResponse<{ user: User, token: string }> = await authAPI.login(email, password)

      if (response.success && response.data) {
        user.value = response.data.user
        isAuthenticated.value = true

        // 保存到本地存储
        localStorage.setItem('user', JSON.stringify(response.data.user))
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('isAuthenticated', 'true')

        return { success: true, message: response.message }
      } else {
        return { success: false, error: response.message }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '登录失败，请检查邮箱和密码'
      }
    } finally {
      loading.value = false
    }
  }

  const register = async (email: string, password: string, verificationCode: string) => {
    loading.value = true
    try {
      const response: any = await authAPI.register(
        email,
        password,
        verificationCode
      )

      // 处理后端 {code: 0, message: "", data: {}} 格式
      if (response.code === 0 && response.data) {
        user.value = response.data.user
        isAuthenticated.value = true
        verificationCodeSent.value = false
        verificationEmail.value = ''

        // 保存到本地存储
        localStorage.setItem('user', JSON.stringify(response.data.user))
        localStorage.setItem('token', response.data.access_token)
        localStorage.setItem('isAuthenticated', 'true')

        return { success: true, message: response.message }
      } else {
        return { success: false, error: response.message }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || error.response?.data?.detail || '注册失败，请稍后重试'
      }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    isAuthenticated.value = false
    verificationCodeSent.value = false
    verificationEmail.value = ''
    localStorage.removeItem('user')
    localStorage.removeItem('token')
    localStorage.removeItem('isAuthenticated')
  }

  const checkAuth = () => {
    const savedUser = localStorage.getItem('user')
    const savedAuth = localStorage.getItem('isAuthenticated')
    const token = localStorage.getItem('token')

    if (savedUser && savedAuth === 'true' && token) {
      user.value = JSON.parse(savedUser)
      isAuthenticated.value = true
      return true
    }
    return false
  }

  const fetchAllowedDomains = async () => {
    try {
      const response: ApiResponse<any> = await authAPI.getAllowedDomains()
      if (response.success && response.data) {
        allowedDomains.value = response.data.categories || {}
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取邮箱域名失败'
      }
    }
  }

  const updateUserProfile = async () => {
    if (!isAuthenticated.value) return

    try {
      const response: ApiResponse<User> = await userAPI.getProfile()
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
        return { success: true }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '更新用户信息失败'
      }
    }
  }

  const canAddMailbox = () => {
    return user.value && user.value.used_mailbox_quota < user.value.daily_mailbox_quota
  }

  return {
    user,
    isAuthenticated,
    loading,
    allowedDomains,
    verificationCodeSent,
    verificationEmail,
    sendVerificationCode,
    login,
    register,
    logout,
    checkAuth,
    fetchAllowedDomains,
    updateUserProfile,
    canAddMailbox
  }
})
