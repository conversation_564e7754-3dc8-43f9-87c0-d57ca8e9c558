import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Mailbox, ApiResponse, AuthCode, AuthCodeType } from '@/types'
import { mailboxAPI, authCodeAPI } from '@/services/api'

export const useMailboxStore = defineStore('mailbox', () => {
  const mailboxes = ref<Mailbox[]>([])
  const loading = ref(false)
  const tempMailbox = ref<Mailbox | null>(null)

  const fetchMailboxes = async () => {
    loading.value = true
    try {
      const response: ApiResponse<Mailbox[]> = await mailboxAPI.getMailboxes()
      if (response.success && response.data) {
        mailboxes.value = response.data
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取邮箱列表失败'
      }
    } finally {
      loading.value = false
    }
  }

  const getTempMailbox = async () => {
    loading.value = true
    try {
      const response: ApiResponse<Mailbox> = await mailboxAPI.getTempMailbox()
      if (response.success && response.data) {
        tempMailbox.value = response.data
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取临时邮箱失败'
      }
    } finally {
      loading.value = false
    }
  }

  const allocateMailbox = async () => {
    loading.value = true
    try {
      const response: ApiResponse<Mailbox> = await mailboxAPI.allocateMailbox()
      if (response.success && response.data) {
        mailboxes.value.push(response.data)
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '申请邮箱失败'
      }
    } finally {
      loading.value = false
    }
  }

  const useAuthCode = async (code: string) => {
    loading.value = true
    try {
      const response: ApiResponse<{ quota_added: number }> = await authCodeAPI.useAuthCode(code)
      if (response.success) {
        return { success: true, data: response.data, message: response.message }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '使用授权码失败'
      }
    } finally {
      loading.value = false
    }
  }

  const deleteMailbox = async (id: number) => {
    loading.value = true
    try {
      const response: ApiResponse = await mailboxAPI.deleteMailbox(id)
      if (response.success) {
        const index = mailboxes.value.findIndex(m => m.id === id)
        if (index > -1) {
          mailboxes.value.splice(index, 1)
        }
        return { success: true, message: response.message }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '删除邮箱失败'
      }
    } finally {
      loading.value = false
    }
  }

  const getStats = async () => {
    try {
      const response: ApiResponse = await mailboxAPI.getStats()
      if (response.success) {
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取统计信息失败'
      }
    }
  }

  return {
    mailboxes,
    tempMailbox,
    loading,
    fetchMailboxes,
    getTempMailbox,
    allocateMailbox,
    useAuthCode,
    deleteMailbox,
    getStats
  }
})
