import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Email, ApiResponse, PaginatedResponse } from '@/types'
import { emailAPI } from '@/services/api'

export const useMailStore = defineStore('mail', () => {
  const emails = ref<Email[]>([])
  const loading = ref(false)
  const selectedEmail = ref<Email | null>(null)
  const totalEmails = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  const fetchRecentEmails = async (params?: any) => {
    loading.value = true
    try {
      const response: PaginatedResponse<Email> = await emailAPI.getRecentEmails(params)
      if (response.success && response.data) {
        emails.value = response.data.items
        totalEmails.value = response.data.total
        currentPage.value = response.data.page
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取邮件列表失败'
      }
    } finally {
      loading.value = false
    }
  }

  const fetchTodayEmails = async (params?: any) => {
    loading.value = true
    try {
      const response: PaginatedResponse<Email> = await emailAPI.getTodayEmails(params)
      if (response.success && response.data) {
        emails.value = response.data.items
        totalEmails.value = response.data.total
        currentPage.value = response.data.page
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取今日邮件失败'
      }
    } finally {
      loading.value = false
    }
  }

  const fetchEmailDetail = async (id: number) => {
    loading.value = true
    try {
      const response: ApiResponse<Email> = await emailAPI.getEmail(id)
      if (response.success && response.data) {
        selectedEmail.value = response.data
        return { success: true, data: response.data }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '获取邮件详情失败'
      }
    } finally {
      loading.value = false
    }
  }

  const markAsRead = async (id: number) => {
    try {
      const response: ApiResponse = await emailAPI.markAsRead(id)
      if (response.success) {
        const email = emails.value.find(e => e.id === id)
        if (email) {
          email.is_read = true
        }
        if (selectedEmail.value && selectedEmail.value.id === id) {
          selectedEmail.value.is_read = true
        }
        return { success: true, message: response.message }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '标记已读失败'
      }
    }
  }

  const deleteEmail = async (id: number) => {
    try {
      const response: ApiResponse = await emailAPI.deleteEmail(id)
      if (response.success) {
        const index = emails.value.findIndex(e => e.id === id)
        if (index > -1) {
          emails.value.splice(index, 1)
          totalEmails.value--
        }
        if (selectedEmail.value && selectedEmail.value.id === id) {
          selectedEmail.value = null
        }
        return { success: true, message: response.message }
      }
      return { success: false, error: response.message }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || '删除邮件失败'
      }
    }
  }

  const selectEmail = async (email: Email) => {
    selectedEmail.value = email
    if (!email.is_read) {
      await markAsRead(email.id)
    }
  }

  const getUnreadCount = () => {
    return emails.value.filter(email => !email.is_read).length
  }

  const getEmailsByMailbox = (mailboxId: number) => {
    return emails.value.filter(email => email.mailbox_id === mailboxId)
  }

  return {
    emails,
    loading,
    selectedEmail,
    totalEmails,
    currentPage,
    pageSize,
    fetchRecentEmails,
    fetchTodayEmails,
    fetchEmailDetail,
    markAsRead,
    deleteEmail,
    selectEmail,
    getUnreadCount,
    getEmailsByMailbox
  }
})
