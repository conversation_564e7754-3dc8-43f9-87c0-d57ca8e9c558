<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-8">
            <div class="flex items-center space-x-3">
              <div class="h-10 w-10 bg-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">转发设置</span>
            </div>

            <div class="hidden md:flex items-center space-x-1">
              <router-link
                to="/"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>邮箱列表</span>
              </router-link>

              <router-link
                to="/forwarding"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-green-100 text-green-700"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>转发设置</span>
              </router-link>

              <router-link
                to="/domains"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
                <span>域名管理</span>
              </router-link>
            </div>
          </div>

          <div class="flex items-center">
            <button @click="showCreateModal = true" class="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors shadow-lg">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              <span>添加转发规则</span>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">邮件转发设置</h1>
        <p class="text-gray-600">配置邮件转发规则，支持全局和单独设置</p>
      </div>

      <!-- 全局转发设置 -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200/50 p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">全局转发设置</h3>
            <p class="text-gray-500">为所有邮箱设置统一的转发规则</p>
          </div>
          <div class="flex items-center">
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="globalForwardingEnabled" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              <span class="ml-3 text-sm font-medium text-gray-900">启用全局转发</span>
            </label>
          </div>
        </div>

        <div v-if="globalForwardingEnabled" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">全局转发邮箱</label>
            <input v-model="globalForwardEmail" type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" placeholder="<EMAIL>" />
          </div>
          <div class="flex items-end">
            <button @click="saveGlobalForwarding" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">保存全局设置</button>
          </div>
        </div>
      </div>

      <!-- 转发规则列表 -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200/50">
        <div class="p-6 border-b border-gray-200/50">
          <h3 class="text-xl font-bold text-gray-900">转发规则列表</h3>
        </div>

        <div v-if="mailStore.loading" class="p-8 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p class="mt-2 text-sm text-gray-500">加载中...</p>
        </div>

        <div v-else-if="mailStore.forwardingRules.length === 0" class="p-8 text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无转发规则</h3>
          <p class="text-gray-500 mb-6">创建您的第一个转发规则</p>
          <button @click="showCreateModal = true" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">创建转发规则</button>
        </div>

        <div v-else class="overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">源邮箱</th>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标邮箱</th>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="rule in mailStore.forwardingRules" :key="rule.id" class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-green-600 font-medium text-sm">{{ rule.fromEmail.charAt(0).toUpperCase() }}</span>
                      </div>
                      <span class="text-sm font-medium text-gray-900">{{ rule.fromEmail }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ rule.toEmail }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="rule.isGlobal ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'" class="px-3 py-1 rounded-full text-xs font-medium">
                      {{ rule.isGlobal ? '全局' : '单独' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" class="px-3 py-1 rounded-full text-xs font-medium">
                      {{ rule.isActive ? '启用' : '停用' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-3">
                      <button @click="toggleRule(rule)" :class="rule.isActive ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'" class="transition-colors">
                        {{ rule.isActive ? '停用' : '启用' }}
                      </button>
                      <button @click="editRule(rule)" class="text-green-600 hover:text-green-700 transition-colors">编辑</button>
                      <button @click="deleteRule(rule)" class="text-red-600 hover:text-red-700 transition-colors">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>

    <!-- 创建/编辑转发规则模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ editingRule ? '编辑转发规则' : '创建转发规则' }}</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="saveRule" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">源邮箱</label>
            <select v-model="ruleForm.fromEmail" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" required>
              <option value="">选择源邮箱</option>
              <option v-for="mailbox in mailboxStore.mailboxes" :key="mailbox.id" :value="mailbox.email">
                {{ mailbox.email }}
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">目标邮箱</label>
            <input v-model="ruleForm.toEmail" type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" placeholder="<EMAIL>" required />
          </div>
          
          <div class="flex items-center">
            <input v-model="ruleForm.isGlobal" type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
            <label class="ml-2 block text-sm text-gray-900">设为全局规则</label>
          </div>
          
          <div class="flex items-center">
            <input v-model="ruleForm.isActive" type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
            <label class="ml-2 block text-sm text-gray-900">立即启用</label>
          </div>
          
          <div class="flex justify-end space-x-3">
            <button type="button" @click="closeModal" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">取消</button>
            <button type="submit" :disabled="mailStore.loading" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50">
              {{ mailStore.loading ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMailboxStore } from '@/stores/auth'
import { useMailStore } from '@/stores/mail'
import type { ForwardingRule } from '@/types'

const mailboxStore = useMailboxStore()
const mailStore = useMailStore()

const showCreateModal = ref(false)
const editingRule = ref<ForwardingRule | null>(null)
const globalForwardingEnabled = ref(false)
const globalForwardEmail = ref('')

const ruleForm = ref({
  fromEmail: '',
  toEmail: '',
  isGlobal: false,
  isActive: true
})

const closeModal = () => {
  showCreateModal.value = false
  editingRule.value = null
  ruleForm.value = {
    fromEmail: '',
    toEmail: '',
    isGlobal: false,
    isActive: true
  }
}

const editRule = (rule: ForwardingRule) => {
  editingRule.value = rule
  ruleForm.value = {
    fromEmail: rule.fromEmail,
    toEmail: rule.toEmail,
    isGlobal: rule.isGlobal,
    isActive: rule.isActive
  }
  showCreateModal.value = true
}

const saveRule = async () => {
  const result = await mailStore.createForwardingRule(ruleForm.value)
  if (result.success) {
    closeModal()
    await mailStore.fetchForwardingRules()
  }
}

const toggleRule = async (rule: ForwardingRule) => {
  // TODO: 调用 API 切换规则状态
  // await api.patch(`/forwarding-rules/${rule.id}`, { isActive: !rule.isActive })
  rule.isActive = !rule.isActive
}

const deleteRule = async (rule: ForwardingRule) => {
  if (confirm('确定要删除这个转发规则吗？')) {
    // TODO: 调用 API 删除规则
    // await api.delete(`/forwarding-rules/${rule.id}`)
    const index = mailStore.forwardingRules.findIndex(r => r.id === rule.id)
    if (index > -1) {
      mailStore.forwardingRules.splice(index, 1)
    }
  }
}

const saveGlobalForwarding = async () => {
  // TODO: 调用 API 保存全局转发设置
  // await api.post('/global-forwarding', {
  //   enabled: globalForwardingEnabled.value,
  //   email: globalForwardEmail.value
  // })
  console.log('保存全局转发设置:', {
    enabled: globalForwardingEnabled.value,
    email: globalForwardEmail.value
  })
}

onMounted(async () => {
  await mailStore.fetchForwardingRules()
})
</script>
