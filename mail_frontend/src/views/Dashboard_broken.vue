<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-green-600 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h1 class="ml-3 text-xl font-semibold text-gray-900">肥猫猫邮箱</h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <div v-if="userStore.isAuthenticated" class="text-sm text-gray-600">
              欢迎，{{ userStore.user?.email }}
            </div>
            <div v-if="userStore.isAuthenticated" class="text-sm text-gray-500">
              今日配额：{{ userStore.user?.used_mailbox_quota || 0 }}/{{ userStore.user?.daily_mailbox_quota || 10 }}
            </div>
            <button
              v-if="userStore.isAuthenticated"
              @click="userStore.logout(); $router.push('/login')"
              class="text-sm text-gray-600 hover:text-gray-900"
            >
              退出登录
            </button>
            <button
              v-else
              @click="$router.push('/login')"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
            >
              登录/注册
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 左侧：邮箱管理和邮件列表 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">邮箱管理</h2>
            
            <!-- 临时邮箱（未登录用户） -->
            <div v-if="!userStore.isAuthenticated" class="space-y-4">
              <div v-if="!mailboxStore.tempMailbox">
                <button
                  @click="getTempMailbox"
                  :disabled="mailboxStore.loading"
                  class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                >
                  <span v-if="mailboxStore.loading">获取中...</span>
                  <span v-else>获取临时邮箱</span>
                </button>
                <p class="mt-2 text-sm text-gray-600">
                  临时用户每天获取1个邮箱，登录后可获得10个
                </p>
              </div>
              
              <div v-else class="p-4 bg-blue-50 rounded-lg">
                <h3 class="font-medium text-blue-900 mb-2">您的临时邮箱</h3>
                <div class="space-y-2">
                  <code class="text-sm text-blue-800 bg-blue-100 px-2 py-1 rounded block break-all">
                    {{ mailboxStore.tempMailbox.email }}
                  </code>
                  <div class="flex justify-end">
                    <button
                      @click="copyToClipboard(mailboxStore.tempMailbox.email)"
                      class="text-blue-600 hover:text-blue-700"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                      </svg>
                    </button>
                  </div>
                </div>
                <p class="text-xs text-blue-700 mt-2">
                  过期时间：{{ mailboxStore.tempMailbox?.expires_at ? formatDate(mailboxStore.tempMailbox.expires_at) : '未知' }}
                </p>
              </div>
            </div>

            <!-- 注册用户邮箱管理 -->
            <div v-else class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">我的邮箱 ({{ mailboxStore.mailboxes.length }})</span>
                <button
                  @click="allocateMailbox"
                  :disabled="mailboxStore.loading || !userStore.canAddMailbox()"
                  class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50"
                >
                  申请邮箱
                </button>
              </div>
              
              <div class="space-y-2 max-h-64 overflow-y-auto">
                <div
                  v-for="mailbox in mailboxStore.mailboxes"
                  :key="mailbox.id"
                  class="p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div class="flex items-center justify-between">
                    <code class="text-sm text-gray-800">{{ mailbox.email }}</code>
                    <div class="flex items-center space-x-2">
                      <button
                        @click="copyToClipboard(mailbox.email)"
                        class="text-gray-400 hover:text-gray-600"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                      </button>
                      <button
                        @click="showDeleteMailboxConfirm(mailbox.id)"
                        class="text-red-400 hover:text-red-600"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    创建时间：{{ formatDate(mailbox.created_at) }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 授权码使用 -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h3 class="text-sm font-medium text-gray-900 mb-3">使用授权码</h3>
              <div class="flex space-x-2">
                <input
                  v-model="authCode"
                  type="text"
                  placeholder="输入授权码"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                />
                <button
                  @click="useAuthCode"
                  :disabled="mailboxStore.loading || !authCode.trim()"
                  class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50"
                >
                  使用
                </button>
              </div>
            </div>
          </div>

          <!-- 邮件列表 -->
          <div class="mt-8">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">最近邮件</h2>
                <div class="flex space-x-2">
                  <button
                    @click="fetchEmails('recent')"
                    :class="[
                      'px-3 py-1 rounded text-sm font-medium',
                      emailFilter === 'recent' 
                        ? 'bg-green-100 text-green-800' 
                        : 'text-gray-600 hover:text-gray-900'
                    ]"
                  >
                    最近
                  </button>
                  <button
                    @click="fetchEmails('today')"
                    :class="[
                      'px-3 py-1 rounded text-sm font-medium',
                      emailFilter === 'today' 
                        ? 'bg-green-100 text-green-800' 
                        : 'text-gray-600 hover:text-gray-900'
                    ]"
                  >
                    今天
                  </button>
                  <button
                    @click="fetchEmails('recent')"
                    class="text-gray-400 hover:text-gray-600"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <div class="divide-y divide-gray-200">
              <div v-if="mailStore.loading" class="p-8 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                <p class="mt-2 text-sm text-gray-600">加载中...</p>
              </div>
              
              <div v-else-if="mailStore.emails.length === 0" class="p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-600">暂无邮件</p>
              </div>
              
              <div
                v-for="email in mailStore.emails"
                :key="email.id"
                @click="selectEmail(email)"
                class="p-4 hover:bg-gray-50 cursor-pointer"
                :class="{ 'bg-blue-50': !email.is_read }"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ email.sender }}
                      </p>
                      <span v-if="!email.is_read" class="inline-block w-2 h-2 bg-blue-600 rounded-full"></span>
                    </div>
                    <p class="text-sm text-gray-600 truncate mt-1">
                      {{ email.subject }}
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                      收件箱：{{ email.mailbox?.email }}
                    </p>
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ formatDate(email.received_at) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：邮件详情 -->
        <div class="lg:col-span-1">
          <div v-if="selectedEmail" class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 border-b border-gray-200">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ selectedEmail.subject }}</h3>
                  <p class="text-sm text-gray-600">
                    <span class="font-medium">发件人：</span>{{ selectedEmail.sender }}
                  </p>
                  <p class="text-sm text-gray-600">
                    <span class="font-medium">收件人：</span>{{ selectedEmail.recipient }}
                  </p>
                  <p class="text-xs text-gray-500 mt-2">
                    {{ formatDate(selectedEmail.received_at) }}
                  </p>
                </div>
                <button
                  @click="selectedEmail = null"
                  class="text-gray-400 hover:text-gray-600 ml-2"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            <div class="p-4 overflow-y-auto max-h-96">
              <div class="prose max-w-none">
                <pre class="whitespace-pre-wrap text-sm text-gray-800">{{ selectedEmail.content }}</pre>
              </div>
            </div>
            <div class="p-4 border-t border-gray-200 flex justify-end">
              <button
                @click="showDeleteEmailConfirm(selectedEmail?.id)"
                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded text-sm font-medium"
              >
                删除邮件
              </button>
            </div>
          </div>
          <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div class="text-gray-400">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <p class="text-sm text-gray-500">选择一封邮件查看详情</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div
      v-if="message"
      class="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50"
      :class="{
        'border-green-200 bg-green-50': messageType === 'success',
        'border-red-200 bg-red-50': messageType === 'error'
      }"
    >
      <div class="flex items-center">
        <svg
          v-if="messageType === 'success'"
          class="w-5 h-5 text-green-600 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <svg
          v-else
          class="w-5 h-5 text-red-600 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <p class="text-sm" :class="{
          'text-green-800': messageType === 'success',
          'text-red-800': messageType === 'error'
        }">
          {{ message }}
        </p>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 z-50 flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 border">
        <div class="flex items-center mb-4">
          <div class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon class="h-6 w-6 text-red-600" />
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
            <p class="text-sm text-gray-500 mt-1">
              {{ deleteType === 'email' ? '确定要删除这封邮件吗？' : '确定要删除这个邮箱吗？' }}
              此操作无法撤销。
            </p>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            @click="cancelDelete"
          >
            取消
          </button>
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
            @click="deleteType === 'email' ? confirmDeleteEmail() : confirmDeleteMailbox()"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'
import { useUserStore } from '@/stores/user'
import { useMailboxStore } from '@/stores/auth'
import { useMailStore } from '@/stores/mail'
import type { Email } from '@/types'

const userStore = useUserStore()
const mailboxStore = useMailboxStore()
const mailStore = useMailStore()

const authCode = ref('')
const selectedEmail = ref<Email | null>(null)
const emailFilter = ref('recent')
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

// 确认对话框状态
const showDeleteConfirm = ref(false)
const deleteEmailId = ref<number | null>(null)
const deleteMailboxId = ref<number | null>(null)
const deleteType = ref<'email' | 'mailbox'>('email')

const showMessage = (msg: string, type: 'success' | 'error' = 'success') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    showMessage('已复制到剪贴板')
  } catch (error) {
    showMessage('复制失败', 'error')
  }
}

const getTempMailbox = async () => {
  const result = await mailboxStore.getTempMailbox()
  if (result.success) {
    showMessage('临时邮箱获取成功')
    // 获取邮箱成功后，自动获取邮件
    await fetchEmails('recent')
  } else {
    showMessage(result.error || '获取临时邮箱失败', 'error')
  }
}

const allocateMailbox = async () => {
  const result = await mailboxStore.allocateMailbox()
  if (result.success) {
    showMessage('邮箱申请成功')
    await userStore.updateUserProfile()
  } else {
    showMessage(result.error || '邮箱申请失败', 'error')
  }
}

// 显示删除邮箱确认对话框
const showDeleteMailboxConfirm = (id: number) => {
  deleteMailboxId.value = id
  deleteType.value = 'mailbox'
  showDeleteConfirm.value = true
}

// 执行删除邮箱
const confirmDeleteMailbox = async () => {
  if (!deleteMailboxId.value) return

  const result = await mailboxStore.deleteMailbox(deleteMailboxId.value)
  if (result.success) {
    showMessage('邮箱删除成功')
    await userStore.updateUserProfile()
  } else {
    showMessage(result.error || '邮箱删除失败', 'error')
  }

  // 关闭对话框
  showDeleteConfirm.value = false
  deleteMailboxId.value = null
}

const useAuthCode = async () => {
  if (!authCode.value.trim()) return

  const result = await mailboxStore.useAuthCode(authCode.value)
  if (result.success) {
    showMessage(`授权码使用成功，增加配额 ${result.data?.quota_added} 个`)
    authCode.value = ''
    await userStore.updateUserProfile()
  } else {
    showMessage(result.error || '授权码使用失败', 'error')
  }
}

const fetchEmails = async (filter: string) => {
  emailFilter.value = filter

  // 获取当前邮箱ID
  let mailboxId = null
  if (mailboxStore.tempMailbox) {
    // 临时邮箱
    mailboxId = mailboxStore.tempMailbox.id
  } else if (mailboxStore.mailboxes.length > 0) {
    // 注册用户的第一个邮箱
    mailboxId = mailboxStore.mailboxes[0].id
  }

  if (!mailboxId) {
    showMessage('请先获取邮箱', 'error')
    return
  }

  let result
  if (filter === 'today') {
    result = await mailStore.fetchTodayEmails({ mailbox_id: mailboxId, hours: 24 })
  } else {
    // 临时邮箱获取24小时的邮件
    result = await mailStore.fetchRecentEmails({ mailbox_id: mailboxId, hours: 24 })
  }

  if (!result.success) {
    showMessage(result.error || '获取邮件失败', 'error')
  }
}

const selectEmail = async (email: Email) => {
  selectedEmail.value = email
  await mailStore.selectEmail(email)
}

// 显示删除邮件确认对话框
const showDeleteEmailConfirm = (id: number | undefined) => {
  console.log('删除邮件ID:', id)
  if (!id) {
    showMessage('邮件ID无效', 'error')
    return
  }
  deleteEmailId.value = id
  deleteType.value = 'email'
  showDeleteConfirm.value = true
  console.log('显示删除确认框:', showDeleteConfirm.value)
}

// 执行删除邮件
const confirmDeleteEmail = async () => {
  if (!deleteEmailId.value) return

  const result = await mailStore.deleteEmail(deleteEmailId.value)
  if (result.success) {
    showMessage('邮件删除成功')
    selectedEmail.value = null
    // 重新获取邮件列表
    await fetchEmails(emailFilter.value)
  } else {
    showMessage(result.error || '邮件删除失败', 'error')
  }

  // 关闭对话框
  showDeleteConfirm.value = false
  deleteEmailId.value = null
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  deleteEmailId.value = null
  deleteMailboxId.value = null
}

onMounted(async () => {
  if (userStore.isAuthenticated) {
    // 注册用户：获取邮箱列表和用户信息
    await mailboxStore.fetchMailboxes()
    await userStore.updateUserProfile()
    // 如果有邮箱，自动获取邮件
    if (mailboxStore.mailboxes.length > 0) {
      await fetchEmails('recent')
    }
  } else {
    // 游客用户：尝试获取临时邮箱
    const result = await mailboxStore.getTempMailbox()
    if (result.success && mailboxStore.tempMailbox) {
      // 如果成功获取到临时邮箱，自动获取邮件
      await fetchEmails('recent')
    }
  }
})
</script>
