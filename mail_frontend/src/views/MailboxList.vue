<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-8">
            <div class="flex items-center space-x-3">
              <div class="h-10 w-10 bg-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">邮箱管理</span>
            </div>
            
            <div class="hidden md:flex items-center space-x-1">
              <router-link
                to="/"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                :class="$route.path === '/' ? 'bg-green-100 text-green-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>邮箱列表</span>
              </router-link>
              
              <router-link
                to="/forwarding"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                :class="$route.path === '/forwarding' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>转发设置</span>
              </router-link>
              
              <router-link
                v-if="userStore.user?.isAdmin"
                to="/domains"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                :class="$route.path === '/domains' ? 'bg-green-100 text-green-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
                <span>域名管理</span>
              </router-link>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <div v-if="userStore.isAuthenticated" class="text-right">
              <p class="text-sm font-medium text-gray-900">{{ userStore.user?.username }}</p>
              <p class="text-xs text-gray-500">今日已获取 {{ userStore.user?.usedMailboxesToday }} / {{ userStore.user?.dailyMailboxLimit }} 个邮箱</p>
            </div>
            <button v-if="userStore.isAuthenticated" @click="showAddModal = true" :disabled="!userStore.canAddMailbox()" class="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              <span>添加邮箱</span>
            </button>
            <router-link v-if="!userStore.isAuthenticated" to="/login" class="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors shadow-lg">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              <span>登录</span>
            </router-link>
            <button v-if="userStore.isAuthenticated" @click="logout" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 未登录用户的介绍页面 -->
      <div v-if="!userStore.isAuthenticated" class="text-center py-16">
        <div class="max-w-3xl mx-auto">
          <div class="w-24 h-24 mx-auto mb-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 class="text-4xl font-bold text-gray-900 mb-6">专业邮箱管理服务</h1>
          <p class="text-xl text-gray-600 mb-8">轻松管理多个邮箱账号，支持邮件收发和智能转发</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div class="text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">多邮箱管理</h3>
              <p class="text-gray-600">每日可获取10个邮箱账号，统一管理多个邮箱</p>
            </div>

            <div class="text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">智能转发</h3>
              <p class="text-gray-600">支持单独和全局转发设置，邮件自动转发到指定邮箱</p>
            </div>
          </div>

          <div class="flex justify-center space-x-4">
            <router-link to="/login" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
              立即开始
            </router-link>
            <a href="#features" class="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              了解更多
            </a>
          </div>
        </div>
      </div>

      <!-- 已登录用户的邮箱管理 -->
      <div v-else>
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">我的邮箱</h1>
          <p class="text-gray-600">管理您的邮箱账号，今日还可获取 {{ remainingMailboxes }} 个邮箱</p>
        </div>

        <!-- 邮箱网格 -->
        <div v-if="mailboxStore.mailboxes.length === 0" class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无邮箱</h3>
        <p class="text-gray-500 mb-6">使用授权码添加您的第一个邮箱</p>
          <button @click="showAddModal = true" :disabled="!userStore.canAddMailbox()" class="inline-flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span>{{ userStore.canAddMailbox() ? '添加邮箱' : '今日次数已用完' }}</span>
          </button>
        </div>

        <div v-else class="space-y-4">
        <div
          v-for="mailbox in mailboxStore.mailboxes"
          :key="mailbox.id"
          class="bg-white rounded-2xl shadow-sm border border-gray-200/50 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group"
          @click="openMailbox(mailbox)"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-14 h-14 bg-slate-600 rounded-xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                {{ mailbox.email.charAt(0).toUpperCase() }}
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors">{{ mailbox.email }}</h3>
                <p class="text-gray-500">{{ mailbox.domain }}</p>
                <div class="flex items-center space-x-4 mt-2">
                  <span :class="mailbox.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'" class="px-3 py-1 rounded-full text-sm font-medium">
                    {{ mailbox.isActive ? '活跃' : '停用' }}
                  </span>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a1 1 0 001 1h8a1 1 0 001-1V8a1 1 0 00-1-1h-1" />
                    </svg>
                    <span>注册于 {{ formatDate(mailbox.createdAt) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-6">
              <div class="text-right">
                <div class="text-2xl font-bold" :class="mailbox.usedCount >= mailbox.dailyLimit ? 'text-red-600' : 'text-gray-900'">
                  {{ mailbox.usedCount }} / {{ mailbox.dailyLimit }}
                </div>
                <p class="text-sm text-gray-500 mb-2">今日使用次数</p>
                <div class="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="mailbox.usedCount >= mailbox.dailyLimit ? 'bg-red-500' : 'bg-green-500'"
                    :style="{ width: `${Math.min(100, (mailbox.usedCount / mailbox.dailyLimit) * 100)}%` }"
                  ></div>
                </div>
              </div>

              <button @click.stop="removeMailbox(mailbox.id)" class="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all p-2 rounded-lg hover:bg-red-50">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </main>

    <!-- 添加邮箱模态框 -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-gray-900">添加邮箱</h3>
            <button @click="showAddModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form @submit.prevent="addMailbox" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">授权码</label>
              <input
                v-model="authCode"
                type="text"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="请输入授权码"
                :disabled="mailboxStore.loading"
              />
            </div>
            
            <div v-if="error" class="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
              {{ error }}
            </div>

            <div v-if="!userStore.canAddMailbox()" class="text-amber-600 text-sm bg-amber-50 p-3 rounded-lg">
              今日邮箱获取次数已用完，明天再来吧！
            </div>
            
            <div class="flex space-x-3 pt-4">
              <button
                type="button"
                @click="showAddModal = false"
                class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="mailboxStore.loading || !authCode.trim() || !userStore.canAddMailbox()"
                class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="mailboxStore.loading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  添加中...
                </span>
                <span v-else>添加邮箱</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMailboxStore } from '@/stores/auth'
import { useUserStore } from '@/stores/user'
import type { Mailbox } from '@/types'

const router = useRouter()
const mailboxStore = useMailboxStore()
const userStore = useUserStore()

const showAddModal = ref(false)
const authCode = ref('')
const error = ref('')

const remainingMailboxes = computed(() => {
  if (!userStore.user) return 0
  return userStore.user.dailyMailboxLimit - userStore.user.usedMailboxesToday
})

const addMailbox = async () => {
  error.value = ''

  if (!userStore.canAddMailbox()) {
    error.value = '今日邮箱获取次数已用完'
    return
  }

  const result = await mailboxStore.addMailboxByAuthCode(authCode.value)

  if (result.success) {
    userStore.updateMailboxUsage()
    showAddModal.value = false
    authCode.value = ''
  } else {
    error.value = result.error || '添加失败，请检查授权码'
  }
}

const removeMailbox = (id: string) => {
  if (confirm('确定要删除这个邮箱吗？')) {
    mailboxStore.removeMailbox(id)
  }
}

const openMailbox = (mailbox: Mailbox) => {
  router.push(`/mailbox/${mailbox.id}`)
}

const logout = () => {
  userStore.logout()
  router.push('/login')
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}
</script>
