<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-green-600 rounded-2xl flex items-center justify-center shadow-lg">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">肥猫猫邮箱</h2>
        <p class="mt-2 text-sm text-gray-600">{{ isLoginMode ? '登录您的账户' : '创建新账户' }}</p>
      </div>
      
      <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
            <div class="relative">
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                placeholder="请输入邮箱地址"
                :disabled="userStore.loading"
              />
              <button
                v-if="!isLoginMode && !userStore.verificationCodeSent"
                type="button"
                @click="showDomainHelp = !showDomainHelp"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </button>
            </div>

            <!-- 邮箱域名提示 -->
            <div v-if="!isLoginMode && showDomainHelp" class="mt-2 p-3 bg-blue-50 rounded-lg text-sm">
              <p class="text-blue-800 font-medium mb-2">支持的邮箱域名：</p>
              <div class="text-blue-700 space-y-1">
                <p><strong>国内：</strong>163.com, qq.com, 126.com, sina.com, 139.com</p>
                <p><strong>国外：</strong>gmail.com, outlook.com, hotmail.com, yahoo.com</p>
                <p class="text-xs text-blue-600 mt-2">更多域名请查看完整列表</p>
              </div>
            </div>
          </div>
          
          <!-- 验证码步骤 -->
          <div v-if="!isLoginMode && !userStore.verificationCodeSent">
            <button
              type="button"
              @click="sendVerificationCode"
              :disabled="userStore.loading || !form.email"
              class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="userStore.loading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                发送中...
              </span>
              <span v-else>发送验证码</span>
            </button>
          </div>

          <!-- 验证码输入 -->
          <div v-if="!isLoginMode && userStore.verificationCodeSent">
            <label for="verificationCode" class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
            <div class="flex space-x-3">
              <input
                id="verificationCode"
                v-model="form.verificationCode"
                type="text"
                required
                maxlength="6"
                class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                placeholder="请输入6位验证码"
                :disabled="userStore.loading"
              />
              <button
                type="button"
                @click="sendVerificationCode"
                :disabled="userStore.loading"
                class="px-4 py-3 text-sm text-blue-600 hover:text-blue-700 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors disabled:opacity-50"
              >
                重发
              </button>
            </div>
            <p class="mt-2 text-sm text-gray-600">
              验证码已发送至 {{ userStore.verificationEmail }}
            </p>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              placeholder="请输入密码"
              :disabled="userStore.loading"
            />
          </div>

          <div v-if="!isLoginMode">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              placeholder="请再次输入密码"
              :disabled="userStore.loading"
            />
          </div>
          
          <div v-if="error" class="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
            {{ error }}
          </div>
          
          <button
            v-if="isLoginMode || (userStore.verificationCodeSent && form.verificationCode)"
            type="submit"
            :disabled="userStore.loading || !isFormValid"
            class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="userStore.loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoginMode ? '登录中...' : '注册中...' }}
            </span>
            <span v-else>{{ isLoginMode ? '登录' : '完成注册' }}</span>
          </button>
        </form>
        
        <div class="mt-6 text-center">
          <button
            @click="toggleMode"
            class="text-green-600 hover:text-green-700 text-sm font-medium transition-colors"
          >
            {{ isLoginMode ? '没有账户？立即注册' : '已有账户？立即登录' }}
          </button>
        </div>
        
        <div v-if="!isLoginMode" class="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 class="text-sm font-medium text-green-900 mb-2">账户说明</h4>
          <ul class="text-sm text-green-700 space-y-1">
            <li>• 注册用户每天可申请 10 个临时邮箱</li>
            <li>• 未注册用户每天可获取 1 个临时邮箱</li>
            <li>• 支持使用授权码增加邮箱配额</li>
            <li>• 邮箱地址完全随机生成</li>
          </ul>
        </div>

        <div class="mt-6 text-center">
          <button
            @click="$router.push('/')"
            class="text-gray-600 hover:text-gray-700 text-sm font-medium transition-colors"
          >
            无需注册，直接使用临时邮箱
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const isLoginMode = ref(true)
const error = ref('')
const showDomainHelp = ref(false)

const form = ref({
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

const isFormValid = computed(() => {
  if (isLoginMode.value) {
    return form.value.email.trim() && form.value.password.trim()
  } else {
    return (
      form.value.email.trim() &&
      form.value.password.trim() &&
      form.value.confirmPassword.trim() &&
      form.value.password === form.value.confirmPassword &&
      userStore.verificationCodeSent &&
      form.value.verificationCode.trim()
    )
  }
})

const toggleMode = () => {
  isLoginMode.value = !isLoginMode.value
  error.value = ''
  showDomainHelp.value = false
  form.value = {
    email: '',
    password: '',
    confirmPassword: '',
    verificationCode: ''
  }
  // 重置验证码状态
  userStore.verificationCodeSent = false
  userStore.verificationEmail = ''
}

const sendVerificationCode = async () => {
  if (!form.value.email.trim()) {
    error.value = '请输入邮箱地址'
    return
  }

  error.value = ''
  const result = await userStore.sendVerificationCode(form.value.email)

  if (!result.success) {
    error.value = result.error || '发送验证码失败'
  }
}

const handleSubmit = async () => {
  error.value = ''

  if (!isLoginMode.value && form.value.password !== form.value.confirmPassword) {
    error.value = '两次输入的密码不一致'
    return
  }

  let result
  if (isLoginMode.value) {
    result = await userStore.login(form.value.email, form.value.password)
  } else {
    result = await userStore.register(
      form.value.email,
      form.value.password,
      form.value.verificationCode
    )
  }

  if (result.success) {
    if (isLoginMode.value) {
      // 登录成功，跳转到首页
      router.push('/')
    } else {
      // 注册成功，显示提示并跳转到登录
      alert(result.message || '注册成功！')
      isLoginMode.value = true
      // 清空表单
      form.value = {
        email: '',
        password: '',
        confirmPassword: '',
        verificationCode: ''
      }
      // 重置验证码状态
      userStore.verificationCodeSent = false
      userStore.verificationEmail = ''
    }
  } else {
    error.value = result.error || (isLoginMode.value ? '登录失败' : '注册失败')
  }
}
</script>
