<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="max-w-md w-full space-y-8 p-8">
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">邮件服务管理</h2>
        <p class="mt-2 text-sm text-gray-600">请输入您的授权码以访问邮箱管理功能</p>
      </div>
      
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <div class="card">
          <div class="space-y-4">
            <div>
              <label for="authCode" class="block text-sm font-medium text-gray-700 mb-2">
                授权码
              </label>
              <input
                id="authCode"
                v-model="authCodeInput"
                type="password"
                required
                class="input-field"
                placeholder="请输入您的授权码"
                :disabled="authStore.loading"
              />
            </div>
            
            <div v-if="error" class="text-red-600 text-sm">
              {{ error }}
            </div>
            
            <button
              type="submit"
              :disabled="authStore.loading || !authCodeInput.trim()"
              class="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="authStore.loading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                验证中...
              </span>
              <span v-else>验证授权码</span>
            </button>
          </div>
        </div>
      </form>
      
      <div class="text-center">
        <p class="text-xs text-gray-500">
          授权码由系统管理员提供，用于访问多个邮箱账号
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const authCodeInput = ref('')
const error = ref('')

const handleSubmit = async () => {
  error.value = ''
  
  const result = await authStore.authenticate(authCodeInput.value)
  
  if (result.success) {
    router.push('/')
  } else {
    error.value = result.error || '验证失败，请检查授权码'
  }
}
</script>
