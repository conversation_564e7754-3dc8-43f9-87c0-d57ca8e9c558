<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center">
              <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="ml-3 text-xl font-semibold text-gray-900">邮箱管理</span>
            </router-link>
          </div>
          
          <div class="flex items-center space-x-4">
            <router-link to="/" class="text-gray-500 hover:text-gray-900 text-sm">返回仪表板</router-link>
            <button @click="authStore.logout" class="btn-secondary text-sm">退出登录</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900">邮箱管理</h1>
        <button @click="showComposeModal = true" class="btn-primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          发送邮件
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 邮箱列表 -->
        <div class="lg:col-span-1">
          <div class="card">
            <h3 class="text-lg font-medium text-gray-900 mb-4">邮箱列表</h3>
            <div class="space-y-2">
              <div
                v-for="mailbox in authStore.mailboxes"
                :key="mailbox.id"
                @click="selectMailbox(mailbox)"
                class="p-3 rounded-lg cursor-pointer transition-colors"
                :class="selectedMailbox?.id === mailbox.id ? 'bg-primary-50 border border-primary-200' : 'hover:bg-gray-50'"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                      <span class="text-primary-600 font-medium text-sm">{{ mailbox.email.charAt(0).toUpperCase() }}</span>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 text-sm">{{ mailbox.email }}</p>
                      <p class="text-xs text-gray-500">{{ mailbox.domain }}</p>
                    </div>
                  </div>
                  <span :class="mailbox.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ mailbox.isActive ? '活跃' : '停用' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件列表 -->
        <div class="lg:col-span-2">
          <div class="card">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">
                {{ selectedMailbox ? `${selectedMailbox.email} 的邮件` : '请选择邮箱' }}
              </h3>
              <button v-if="selectedMailbox" @click="refreshEmails" class="btn-secondary text-sm">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                刷新
              </button>
            </div>
            
            <div v-if="!selectedMailbox" class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">请从左侧选择一个邮箱查看邮件</p>
            </div>
            
            <div v-else-if="mailStore.loading" class="text-center py-12">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p class="mt-2 text-sm text-gray-500">加载中...</p>
            </div>
            
            <div v-else-if="mailStore.emails.length === 0" class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">暂无邮件</p>
            </div>
            
            <div v-else class="space-y-2">
              <div
                v-for="email in mailStore.emails"
                :key="email.id"
                @click="selectEmail(email)"
                class="p-4 rounded-lg border cursor-pointer transition-colors hover:bg-gray-50"
                :class="email.isRead ? 'border-gray-200' : 'border-primary-200 bg-primary-50'"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-1">
                      <span class="font-medium text-gray-900">{{ email.from }}</span>
                      <span v-if="!email.isRead" class="w-2 h-2 bg-primary-600 rounded-full"></span>
                    </div>
                    <p class="text-sm font-medium text-gray-900 mb-1">{{ email.subject }}</p>
                    <p class="text-sm text-gray-600 line-clamp-2">{{ email.content }}</p>
                  </div>
                  <span class="text-xs text-gray-500 ml-4">{{ formatDate(email.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <!-- 发送邮件模态框 -->
    <div v-if="showComposeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">发送邮件</h3>
          <button @click="showComposeModal = false" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="sendEmail" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">发件人</label>
            <select v-model="composeForm.from" class="input-field" required>
              <option value="">选择发件邮箱</option>
              <option v-for="mailbox in authStore.mailboxes.filter(m => m.isActive)" :key="mailbox.id" :value="mailbox.email">
                {{ mailbox.email }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">收件人</label>
            <input v-model="composeForm.to" type="email" class="input-field" placeholder="<EMAIL>" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
            <input v-model="composeForm.subject" type="text" class="input-field" placeholder="邮件主题" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">内容</label>
            <textarea v-model="composeForm.content" rows="6" class="input-field" placeholder="邮件内容" required></textarea>
          </div>

          <div class="flex justify-end space-x-3">
            <button type="button" @click="showComposeModal = false" class="btn-secondary">取消</button>
            <button type="submit" :disabled="mailStore.loading" class="btn-primary">
              {{ mailStore.loading ? '发送中...' : '发送' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 邮件详情模态框 -->
    <div v-if="selectedEmail" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">邮件详情</h3>
          <button @click="selectedEmail = null" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <div class="border-b border-gray-200 pb-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">发件人</label>
                <p class="text-gray-900">{{ selectedEmail.from }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">收件人</label>
                <p class="text-gray-900">{{ selectedEmail.to }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">主题</label>
                <p class="text-gray-900">{{ selectedEmail.subject }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">时间</label>
                <p class="text-gray-900">{{ formatDate(selectedEmail.timestamp) }}</p>
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-500 mb-2">内容</label>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="whitespace-pre-wrap">{{ selectedEmail.content }}</p>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button @click="replyToEmail" class="btn-primary">回复</button>
            <button @click="selectedEmail = null" class="btn-secondary">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMailStore } from '@/stores/mail'
import type { Mailbox, Email } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const mailStore = useMailStore()

const selectedMailbox = ref<Mailbox | null>(null)
const selectedEmail = ref<Email | null>(null)
const showComposeModal = ref(false)

const composeForm = ref({
  from: '',
  to: '',
  subject: '',
  content: ''
})

const selectMailbox = async (mailbox: Mailbox) => {
  selectedMailbox.value = mailbox
  await mailStore.fetchEmails(mailbox.id)
}

const selectEmail = (email: Email) => {
  selectedEmail.value = email
  // TODO: 标记邮件为已读
  // await api.patch(`/emails/${email.id}`, { isRead: true })
}

const refreshEmails = async () => {
  if (selectedMailbox.value) {
    await mailStore.fetchEmails(selectedMailbox.value.id)
  }
}

const sendEmail = async () => {
  const result = await mailStore.sendEmail(composeForm.value)
  if (result.success) {
    showComposeModal.value = false
    composeForm.value = { from: '', to: '', subject: '', content: '' }
    // 可以显示成功消息
  }
}

const replyToEmail = () => {
  if (selectedEmail.value) {
    composeForm.value = {
      from: selectedEmail.value.to,
      to: selectedEmail.value.from,
      subject: `Re: ${selectedEmail.value.subject}`,
      content: `\n\n--- 原始邮件 ---\n发件人: ${selectedEmail.value.from}\n主题: ${selectedEmail.value.subject}\n\n${selectedEmail.value.content}`
    }
    selectedEmail.value = null
    showComposeModal.value = true
  }
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/auth')
  }
})
</script>
