<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-8">
            <div class="flex items-center space-x-3">
              <button @click="$router.back()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div class="h-10 w-10 bg-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <span class="text-xl font-bold text-gray-900">{{ mailbox?.email }}</span>
                <p class="text-sm text-gray-500">邮箱管理</p>
              </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-1">
              <router-link
                to="/"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>邮箱列表</span>
              </router-link>
              
              <router-link
                to="/forwarding"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>转发设置</span>
              </router-link>
              
              <router-link
                to="/domains"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
                <span>域名管理</span>
              </router-link>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <button @click="refreshEmails" class="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>刷新</span>
            </button>
            <button @click="showComposeModal = true" class="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors shadow-lg">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              <span>发送邮件</span>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 邮箱信息卡片 -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200/50 p-6 mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-slate-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg">
              {{ mailbox?.email.charAt(0).toUpperCase() }}
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">{{ mailbox?.email }}</h1>
              <p class="text-gray-500">{{ mailbox?.domain }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <span :class="mailbox?.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'" class="px-3 py-1 rounded-full text-sm font-medium">
                  {{ mailbox?.isActive ? '活跃' : '停用' }}
                </span>
                <span class="text-sm text-gray-500">注册于 {{ formatDate(mailbox?.createdAt) }}</span>
              </div>
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-3xl font-bold" :class="mailbox && mailbox.usedCount >= mailbox.dailyLimit ? 'text-red-600' : 'text-gray-900'">
              {{ mailbox?.usedCount }} / {{ mailbox?.dailyLimit }}
            </div>
            <p class="text-sm text-gray-500">今日使用次数</p>
            <div class="w-32 bg-gray-200 rounded-full h-2 mt-2">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="mailbox && mailbox.usedCount >= mailbox.dailyLimit ? 'bg-red-500' : 'bg-green-500'"
                :style="{ width: `${mailbox ? Math.min(100, (mailbox.usedCount / mailbox.dailyLimit) * 100) : 0}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 邮件列表 -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200/50">
        <div class="p-6 border-b border-gray-200/50">
          <h2 class="text-xl font-bold text-gray-900">邮件列表</h2>
        </div>

        <div v-if="mailStore.loading" class="p-8 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p class="mt-2 text-sm text-gray-500">加载中...</p>
        </div>
        
        <div v-else-if="mailStore.emails.length === 0" class="p-8 text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无邮件</h3>
          <p class="text-gray-500">这个邮箱还没有收到任何邮件</p>
        </div>
        
        <div v-else class="divide-y divide-gray-200/50">
          <div
            v-for="email in mailStore.emails"
            :key="email.id"
            @click="selectEmail(email)"
            class="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
            :class="!email.isRead ? 'bg-green-50/50' : ''"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <span class="font-semibold text-gray-900">{{ email.from }}</span>
                  <span v-if="!email.isRead" class="w-2 h-2 bg-green-600 rounded-full"></span>
                </div>
                <h3 class="font-medium text-gray-900 mb-1">{{ email.subject }}</h3>
                <p class="text-gray-600 text-sm line-clamp-2">{{ email.content }}</p>
              </div>
              <span class="text-sm text-gray-500 ml-4">{{ formatEmailDate(email.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 发送邮件模态框 -->
    <div v-if="showComposeModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-gray-900">发送邮件</h3>
            <button @click="showComposeModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form @submit.prevent="sendEmail" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">发件人</label>
              <input
                :value="mailbox?.email"
                disabled
                class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">收件人</label>
              <input
                v-model="composeForm.to"
                type="email"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">主题</label>
              <input
                v-model="composeForm.subject"
                type="text"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                placeholder="邮件主题"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">内容</label>
              <textarea
                v-model="composeForm.content"
                rows="6"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none"
                placeholder="邮件内容"
              ></textarea>
            </div>
            
            <div class="flex space-x-3 pt-4">
              <button
                type="button"
                @click="showComposeModal = false"
                class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="mailStore.loading || (mailbox && mailbox.usedCount >= mailbox.dailyLimit)"
                class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="mailStore.loading">发送中...</span>
                <span v-else-if="mailbox && mailbox.usedCount >= mailbox.dailyLimit">今日次数已用完</span>
                <span v-else>发送邮件</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 邮件详情模态框 -->
    <div v-if="selectedEmail" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-gray-900">邮件详情</h3>
            <button @click="selectedEmail = null" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="space-y-6">
            <div class="bg-gray-50 rounded-xl p-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">发件人</label>
                  <p class="text-gray-900">{{ selectedEmail.from }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">收件人</label>
                  <p class="text-gray-900">{{ selectedEmail.to }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">主题</label>
                  <p class="text-gray-900">{{ selectedEmail.subject }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">时间</label>
                  <p class="text-gray-900">{{ formatEmailDate(selectedEmail.timestamp) }}</p>
                </div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500 mb-3">内容</label>
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <p class="whitespace-pre-wrap text-gray-900 leading-relaxed">{{ selectedEmail.content }}</p>
              </div>
            </div>
            
            <div class="flex justify-end space-x-3">
              <button @click="replyToEmail" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                回复
              </button>
              <button @click="selectedEmail = null" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useMailboxStore } from '@/stores/auth'
import { useMailStore } from '@/stores/mail'
import type { Email } from '@/types'

const route = useRoute()
const mailboxStore = useMailboxStore()
const mailStore = useMailStore()

const showComposeModal = ref(false)
const selectedEmail = ref<Email | null>(null)

const composeForm = ref({
  to: '',
  subject: '',
  content: ''
})

const mailbox = computed(() => {
  return mailboxStore.mailboxes.find(m => m.id === route.params.id)
})

const refreshEmails = async () => {
  if (mailbox.value) {
    await mailStore.fetchEmails(mailbox.value.id)
  }
}

const sendEmail = async () => {
  if (!mailbox.value || mailbox.value.usedCount >= mailbox.value.dailyLimit) {
    return
  }

  const emailData = {
    from: mailbox.value.email,
    to: composeForm.value.to,
    subject: composeForm.value.subject,
    content: composeForm.value.content
  }

  const result = await mailStore.sendEmail(emailData)
  if (result.success) {
    showComposeModal.value = false
    composeForm.value = { to: '', subject: '', content: '' }
    mailboxStore.updateMailboxUsage(mailbox.value.id)
    await refreshEmails()
  }
}

const selectEmail = (email: Email) => {
  selectedEmail.value = email
  // TODO: 标记邮件为已读
}

const replyToEmail = () => {
  if (selectedEmail.value) {
    composeForm.value = {
      to: selectedEmail.value.from,
      subject: `Re: ${selectedEmail.value.subject}`,
      content: `\n\n--- 原始邮件 ---\n发件人: ${selectedEmail.value.from}\n主题: ${selectedEmail.value.subject}\n\n${selectedEmail.value.content}`
    }
    selectedEmail.value = null
    showComposeModal.value = true
  }
}

const formatDate = (date?: Date) => {
  if (!date) return ''
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

const formatEmailDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

onMounted(() => {
  if (mailbox.value) {
    refreshEmails()
  }
})
</script>
